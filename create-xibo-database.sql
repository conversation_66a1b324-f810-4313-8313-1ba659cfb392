-- Script SQL pour créer la base de données Xibo CMS
-- À exécuter dans phpMyAdmin ou MySQL CLI
-- 
-- Instructions:
-- 1. <PERSON>uvrir phpMyAdmin (http://localhost/phpmyadmin)
-- 2. <PERSON><PERSON><PERSON> sur "SQL" dans le menu principal
-- 3. <PERSON><PERSON><PERSON>-coller ce script et l'exécuter
-- 4. Ou utiliser MySQL CLI: mysql -u root -p < create-xibo-database.sql

-- =====================================================
-- CRÉATION DE LA BASE DE DONNÉES XIBO
-- =====================================================

-- Supprimer la base si elle existe déjà (ATTENTION: supprime toutes les données)
-- DROP DATABASE IF EXISTS xibo_cms;

-- Créer la base de données avec le bon charset
CREATE DATABASE IF NOT EXISTS xibo_cms 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Vérifier la création
SHOW DATABASES LIKE 'xibo_cms';

-- =====================================================
-- CRÉATION DE L'UTILISATEUR XIBO
-- =====================================================

-- Supprimer l'utilisateur s'il existe déjà
DROP USER IF EXISTS 'xibo_user'@'localhost';

-- Créer l'utilisateur avec un mot de passe sécurisé
-- IMPORTANT: Changez 'XiboSecurePass2024!' par votre propre mot de passe
CREATE USER 'xibo_user'@'localhost' IDENTIFIED BY 'XiboSecurePass2024!';

-- Accorder tous les privilèges sur la base xibo_cms
GRANT ALL PRIVILEGES ON xibo_cms.* TO 'xibo_user'@'localhost';

-- Privilèges spécifiques pour Xibo (optionnel, plus sécurisé)
-- GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES 
-- ON xibo_cms.* TO 'xibo_user'@'localhost';

-- Appliquer les changements
FLUSH PRIVILEGES;

-- =====================================================
-- VÉRIFICATIONS
-- =====================================================

-- Vérifier que la base de données existe
SELECT SCHEMA_NAME as 'Base de données créée'
FROM INFORMATION_SCHEMA.SCHEMATA 
WHERE SCHEMA_NAME = 'xibo_cms';

-- Vérifier que l'utilisateur existe
SELECT User, Host, authentication_string as 'Mot de passe hashé'
FROM mysql.user 
WHERE User = 'xibo_user';

-- Vérifier les privilèges accordés
SHOW GRANTS FOR 'xibo_user'@'localhost';

-- =====================================================
-- CONFIGURATION MYSQL POUR XIBO
-- =====================================================

-- Vérifier la configuration MySQL actuelle
SELECT @@sql_mode as 'SQL Mode actuel';
SELECT @@max_allowed_packet as 'Max packet size';
SELECT @@innodb_buffer_pool_size as 'InnoDB buffer pool';

-- Recommandations pour my.ini/my.cnf:
-- [mysqld]
-- sql_mode = ""
-- max_allowed_packet = 64M
-- innodb_buffer_pool_size = 256M
-- innodb_file_per_table = 1
-- character-set-server = utf8mb4
-- collation-server = utf8mb4_unicode_ci

-- =====================================================
-- TEST DE CONNEXION
-- =====================================================

-- Test de connexion avec le nouvel utilisateur
-- (À exécuter après avoir fermé la session root)
-- mysql -u xibo_user -p xibo_cms

-- =====================================================
-- INFORMATIONS POUR L'INSTALLATION XIBO
-- =====================================================

/*
Informations à utiliser lors de l'installation web de Xibo:

Host de base de données: localhost
Nom de la base: xibo_cms
Utilisateur: xibo_user
Mot de passe: XiboSecurePass2024! (ou celui que vous avez choisi)

URL d'installation: http://xibo.local ou http://localhost/xibo

Dossier Library: C:\xampp\htdocs\xibo\library
*/

-- =====================================================
-- SCRIPT DE SAUVEGARDE (pour référence)
-- =====================================================

/*
Pour sauvegarder la base de données plus tard:
mysqldump -u xibo_user -p xibo_cms > xibo_backup_YYYYMMDD.sql

Pour restaurer:
mysql -u xibo_user -p xibo_cms < xibo_backup_YYYYMMDD.sql
*/

-- =====================================================
-- NETTOYAGE (si nécessaire)
-- =====================================================

/*
Pour supprimer complètement Xibo (ATTENTION: perte de données):

DROP DATABASE xibo_cms;
DROP USER 'xibo_user'@'localhost';
FLUSH PRIVILEGES;
*/

-- Fin du script
SELECT 'Base de données Xibo créée avec succès!' as 'Statut';
SELECT 'Vous pouvez maintenant procéder à l\'installation web de Xibo' as 'Prochaine étape';
