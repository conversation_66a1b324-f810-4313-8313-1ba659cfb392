# Guide d'installation web Xibo CMS 4.2.3 - Configuration HP

## Situation actuelle
- **Xibo CMS 4.2.3** téléchargé dans `C:\Users\<USER>\Downloads\Prodynamique\xibo-cms-4.2.3`
- **XAMPP** déjà installé
- **Scripts de configuration** créés et prêts

## Étapes d'installation dans l'ordre

### 1. Exécuter les scripts de configuration

#### Script 1: Déplacer Xibo CMS
```batch
# Clic droit "Exécuter en tant qu'administrateur"
move-configure-xibo.bat
```
**Résultat**: Xibo déplacé vers `C:\xampp\htdocs\xibo`

#### Script 2: Configurer XAMPP
```batch
# Clic droit "Exécuter en tant qu'administrateur"
configure-xampp-for-xibo.bat
```
**Résultat**: PHP et Apache configurés pour Xibo

#### Script 3: Finaliser les permissions
```batch
# Clic droit "Exécuter en tant qu'administrateur"
final-setup-permissions.bat
```
**Résultat**: Permissions et sécurité configurées

### 2. Démarrer les services XAMPP

#### Via le panneau XAMPP
1. **Ouvrir**: `C:\xampp\xampp-control.exe`
2. **Démarrer**: Apache (bouton Start)
3. **Démarrer**: MySQL (bouton Start)
4. **Vérifier**: Les deux services sont verts

#### Via le script automatique
```batch
# Double-clic sur:
C:\xampp\start-xibo.bat
```

### 3. Tester la configuration

#### Test des permissions et PHP
1. **Navigateur**: http://localhost/xibo/test-installation.php
2. **Vérifier**: Tous les éléments sont verts/OK
3. **Corriger**: Les éventuels problèmes avant de continuer

#### Test d'accès Xibo
1. **Navigateur**: http://localhost/xibo
2. **Résultat attendu**: Page d'installation Xibo
3. **Alternative**: http://xibo.local (si Virtual Host configuré)

### 4. Créer la base de données MySQL

#### Via phpMyAdmin
1. **Accéder**: http://localhost/phpmyadmin
2. **Connexion**: root (sans mot de passe par défaut)
3. **Onglet**: SQL
4. **Copier-coller**: Le contenu de `create-xibo-database-hp.sql`
5. **Exécuter**: Le script SQL
6. **Vérifier**: Message de succès

#### Paramètres créés
- **Base de données**: `xibo_cms`
- **Utilisateur**: `xibo_user`
- **Mot de passe**: `XiboHP2025!`

### 5. Lancer l'installation web Xibo

#### Accès à l'installateur
1. **URL**: http://localhost/xibo
2. **Page**: Installation wizard Xibo

#### Étape 1: Prerequisites Check
**Vérifications automatiques**:
- ✅ PHP Version 8.x
- ✅ Extensions PHP requises
- ✅ File Permissions (library, cache)
- ✅ Database connectivity
- ✅ URL Rewriting

**Si problèmes**: Corriger et cliquer "Retest"

#### Étape 2: Database Configuration
**Sélectionner**: "Existing Database"

**Paramètres à saisir**:
- **Database Host**: `localhost`
- **Database Name**: `xibo_cms`
- **Database Username**: `xibo_user`
- **Database Password**: `XiboHP2025!`

**Cliquer**: "Test Connection" puis "Next"

#### Étape 3: Database Installation
- **Processus**: Installation automatique des tables
- **Durée**: 2-5 minutes
- **Indicateur**: Points qui apparaissent progressivement
- **Résultat**: "Installation Complete"

#### Étape 4: Admin User Creation
**Créer le compte administrateur**:
- **Username**: `admin` (ou votre choix)
- **Password**: Mot de passe sécurisé (noter précieusement!)
- **Email**: Votre adresse email

**Recommandation**: Mot de passe de 12+ caractères avec majuscules, minuscules, chiffres et symboles

#### Étape 5: CMS Configuration
**Paramètres système**:
- **Library Location**: `C:\xampp\htdocs\xibo\library`
- **CMS Secret Key**: Laisser générer automatiquement
- **Statistics**: ☑️ Envoyer des statistiques (optionnel)

**Paramètres avancés**:
- **Timezone**: Europe/Paris
- **Language**: English (Français si disponible)

#### Étape 6: Installation Complete
- **Message**: "Installation Complete"
- **Lien**: "Login to Xibo"
- **Première connexion**: Utiliser les identifiants admin créés

### 6. Première connexion et vérification

#### Connexion administrateur
1. **Cliquer**: "Login to Xibo"
2. **Saisir**: Username et password admin
3. **Résultat**: Tableau de bord Xibo

#### Vérifications essentielles
1. **Dashboard**: Accessible sans erreur
2. **Library**: Menu → Library (test d'accès)
3. **Layouts**: Menu → Layouts (test d'accès)
4. **Displays**: Menu → Displays (test d'accès)
5. **Administration**: Menu → Administration → Log (vérifier les logs)

#### Test d'upload
1. **Menu**: Library → Add Media
2. **Upload**: Une image (JPG/PNG < 10MB)
3. **Vérifier**: Upload réussi et miniature générée

### 7. Configuration post-installation

#### Paramètres régionaux
1. **Menu**: Administration → Settings
2. **Onglet**: Regional
3. **Timezone**: Europe/Paris
4. **Date Format**: Format européen
5. **Sauvegarder**: Les modifications

#### Premier layout de test
1. **Menu**: Layouts → Add Layout
2. **Nom**: "Test Layout"
3. **Résolution**: 1920x1080
4. **Créer**: Le layout
5. **Designer**: Ajouter une région avec du texte
6. **Publier**: Le layout

## Paramètres de configuration finaux

### Informations de connexion
- **URL CMS**: http://localhost/xibo ou http://xibo.local
- **Admin Username**: [celui créé lors de l'installation]
- **Admin Password**: [celui créé lors de l'installation]

### Chemins importants
- **Installation**: `C:\xampp\htdocs\xibo`
- **Library**: `C:\xampp\htdocs\xibo\library`
- **Cache**: `C:\xampp\htdocs\xibo\cache`
- **Logs**: `C:\xampp\htdocs\xibo\library\log`

### Base de données
- **Host**: localhost
- **Database**: xibo_cms
- **User**: xibo_user
- **Password**: XiboHP2025!

## Dépannage installation web

### Erreur "Database Connection Failed"
```
Solutions:
1. Vérifier que MySQL est démarré dans XAMPP
2. Contrôler les paramètres de base de données
3. Tester la connexion dans phpMyAdmin
4. Vérifier le script SQL a bien été exécuté
```

### Erreur "Library not writable"
```
Solutions:
1. Exécuter final-setup-permissions.bat en admin
2. Vérifier le chemin: C:\xampp\htdocs\xibo\library
3. Contrôler les permissions Windows
4. Redémarrer Apache
```

### Page blanche ou erreur 500
```
Solutions:
1. Consulter C:\xampp\apache\logs\error.log
2. Vérifier la configuration PHP
3. Contrôler les permissions des fichiers
4. Redémarrer Apache
```

### Timeout pendant l'installation
```
Solutions:
1. Vérifier la configuration PHP (max_execution_time)
2. Contrôler la performance MySQL
3. Redémarrer l'installation
4. Vérifier l'espace disque disponible
```

## Prochaines étapes après installation

### 1. Configuration des tâches automatiques (XTR)
- Configurer le Planificateur de tâches Windows
- Script de maintenance automatique
- Surveillance des logs

### 2. Sécurisation
- Changer les mots de passe par défaut
- Configurer les sauvegardes
- Mettre en place le monitoring

### 3. Contenu et affichage
- Créer des layouts personnalisés
- Uploader vos médias
- Configurer les displays/écrans
- Planifier l'affichage

### 4. Formation et utilisation
- Explorer l'interface d'administration
- Créer des utilisateurs supplémentaires
- Configurer les permissions
- Tester avec des players Xibo

---

**Félicitations !** Votre installation Xibo CMS 4.2.3 est maintenant prête pour la production. Vous disposez d'un système d'affichage dynamique professionnel et fonctionnel.
