# Test et validation finale - Installation Xibo CMS 4.2.3 HP

## Checklist de validation complète

### ✅ Phase 1: Vérifications système

#### 1.1 Services XAMPP
```
□ Apache démarré et fonctionnel
□ MySQL démarré et accessible
□ Aucune erreur dans les logs XAMPP
□ Ports 80 et 3306 disponibles
```

#### 1.2 Structure des fichiers
```
□ C:\xampp\htdocs\xibo\ - Dossier principal
□ C:\xampp\htdocs\xibo\web\ - Interface web
□ C:\xampp\htdocs\xibo\library\ - Stockage médias
□ C:\xampp\htdocs\xibo\cache\ - Cache système
□ C:\xampp\htdocs\xibo\bin\xtr.php - Script maintenance
```

#### 1.3 Permissions et sécurité
```
□ Dossier library accessible en écriture
□ Dossier cache accessible en écriture
□ Fichiers .htaccess de protection présents
□ Test permissions: http://localhost/xibo/test-installation.php
```

### ✅ Phase 2: Base de données

#### 2.1 Connexion MySQL
```
□ Base xibo_cms créée et accessible
□ Utilisateur xibo_user fonctionnel
□ Tables Xibo installées (50+ tables)
□ Données par défaut présentes
```

#### 2.2 Test via phpMyAdmin
```
URL: http://localhost/phpmyadmin
□ Connexion root réussie
□ Base xibo_cms visible
□ Tables: display, layout, media, user, etc.
□ Aucune erreur SQL
```

### ✅ Phase 3: Interface web Xibo

#### 3.1 Accès principal
```
URL: http://localhost/xibo
□ Page de connexion Xibo affichée
□ Aucune erreur 404 ou 500
□ CSS et JavaScript chargés correctement
□ Formulaire de connexion fonctionnel
```

#### 3.2 Connexion administrateur
```
□ Connexion avec compte admin créé
□ Tableau de bord accessible
□ Menu principal complet
□ Aucune erreur JavaScript
```

#### 3.3 Navigation dans l'interface
```
□ Menu Library accessible
□ Menu Layouts accessible
□ Menu Displays accessible
□ Menu Administration accessible
□ Menu Schedule accessible
```

### ✅ Phase 4: Fonctionnalités de base

#### 4.1 Gestion des médias
```
Test: Library → Add Media
□ Interface d'upload affichée
□ Upload d'une image (JPG/PNG) réussi
□ Miniature générée automatiquement
□ Fichier stocké dans library/
□ Métadonnées détectées (taille, durée, etc.)
```

#### 4.2 Création de layout
```
Test: Layouts → Add Layout
□ Formulaire de création affiché
□ Layout créé avec succès
□ Designer accessible
□ Ajout de région possible
□ Ajout de widget texte possible
□ Sauvegarde du layout réussie
```

#### 4.3 Gestion des displays
```
Test: Displays → Add Display
□ Formulaire de création affiché
□ Display créé avec succès
□ Paramètres configurables
□ Statut du display visible
```

#### 4.4 Planification
```
Test: Schedule → Add Event
□ Formulaire de planification affiché
□ Sélection de display possible
□ Sélection de layout possible
□ Événement créé avec succès
□ Calendrier mis à jour
```

### ✅ Phase 5: Tâches automatiques (XTR)

#### 5.1 Configuration XTR
```
□ Script setup-xtr-hp.ps1 exécuté avec succès
□ Tâche planifiée "Xibo Maintenance XTR (HP)" créée
□ Script batch xibo-xtr-maintenance.bat fonctionnel
□ Logs XTR générés dans library/log/
```

#### 5.2 Test d'exécution
```
Test manuel: C:\xampp\htdocs\xibo\xibo-xtr-maintenance.bat
□ Script s'exécute sans erreur
□ Log créé dans library/log/xtr-maintenance.log
□ Aucune erreur PHP
□ Processus terminé correctement
```

#### 5.3 Surveillance automatique
```
□ Tâche planifiée active dans Windows
□ Exécution toutes les minutes
□ Logs régulièrement mis à jour
□ Script de surveillance monitor-xtr.ps1 fonctionnel
```

### ✅ Phase 6: Tests avancés

#### 6.1 Performance
```
□ Temps de chargement < 3 secondes
□ Upload de fichier 10MB réussi
□ Création de layout complexe (5+ régions)
□ Navigation fluide dans l'interface
```

#### 6.2 Stabilité
```
□ Redémarrage PC - Services auto-démarrés
□ Fonctionnement continu 1 heure sans erreur
□ Aucune fuite mémoire détectée
□ Logs sans erreurs critiques
```

#### 6.3 Sécurité
```
□ Accès library/ protégé (403 Forbidden)
□ Accès cache/ protégé (403 Forbidden)
□ Déconnexion automatique après inactivité
□ Mots de passe sécurisés utilisés
```

## Tests pratiques détaillés

### Test 1: Création d'un affichage complet

#### Étape 1: Créer un layout "Bienvenue HP"
1. **Layouts** → **Add Layout**
2. **Nom**: "Bienvenue HP"
3. **Description**: "Premier affichage de test"
4. **Résolution**: 1920x1080
5. **Sauvegarder**

#### Étape 2: Designer le layout
1. **Ouvrir** le layout en mode designer
2. **Ajouter** une région pleine largeur en haut
3. **Widget**: Text
4. **Contenu**: "Bienvenue sur Xibo CMS 4.2.3 - Installation HP"
5. **Style**: Police grande, couleur bleue
6. **Sauvegarder** le widget

#### Étape 3: Ajouter du contenu multimédia
1. **Ajouter** une région pour image
2. **Widget**: Image
3. **Uploader** une image de test
4. **Configurer** la durée d'affichage
5. **Sauvegarder**

#### Étape 4: Publier le layout
1. **Retour** à la liste des layouts
2. **Actions** → **Publish**
3. **Confirmer** la publication

### Test 2: Simulation d'affichage

#### Créer un display virtuel
1. **Displays** → **Add Display**
2. **Nom**: "Écran Test HP"
3. **Type**: Windows Player
4. **Sauvegarder**

#### Programmer l'affichage
1. **Schedule** → **Add Event**
2. **Display**: "Écran Test HP"
3. **Layout**: "Bienvenue HP"
4. **Horaire**: Maintenant → Dans 2 heures
5. **Sauvegarder**

#### Vérifier la programmation
1. **Schedule** → **Calendar View**
2. **Vérifier** l'événement affiché
3. **Display** → **Screen Shot** (si disponible)

### Test 3: Gestion des utilisateurs

#### Créer un utilisateur test
1. **Administration** → **Users** → **Add User**
2. **Username**: "testuser"
3. **Email**: "<EMAIL>"
4. **Password**: Mot de passe sécurisé
5. **Group**: User
6. **Sauvegarder**

#### Tester la connexion
1. **Se déconnecter** du compte admin
2. **Se connecter** avec testuser
3. **Vérifier** les permissions limitées
4. **Se reconnecter** en admin

## Validation des logs

### Logs à vérifier

#### 1. Logs Xibo CMS
```
Fichier: C:\xampp\htdocs\xibo\library\log\cms.log
Rechercher:
□ Aucune erreur CRITICAL
□ Connexions utilisateur réussies
□ Opérations de création/modification
□ Activité XTR régulière
```

#### 2. Logs XTR
```
Fichier: C:\xampp\htdocs\xibo\library\log\xtr-maintenance.log
Vérifier:
□ Exécutions toutes les minutes
□ Aucune erreur PHP
□ Tâches de maintenance terminées
□ Nettoyage automatique des anciens logs
```

#### 3. Logs Apache
```
Fichier: C:\xampp\apache\logs\error.log
Contrôler:
□ Aucune erreur 500
□ Aucune erreur de permissions
□ Aucun timeout PHP
□ Modules chargés correctement
```

#### 4. Logs MySQL
```
Fichier: C:\xampp\mysql\data\mysql_error.log
Vérifier:
□ Aucune erreur de connexion
□ Aucune corruption de table
□ Performances acceptables
□ Espace disque suffisant
```

## Résolution des problèmes courants

### Problème: Interface lente
```
Solutions:
1. Vérifier la RAM disponible
2. Optimiser la configuration PHP
3. Nettoyer le cache Xibo
4. Redémarrer Apache
```

### Problème: XTR ne s'exécute pas
```
Solutions:
1. Vérifier la tâche planifiée Windows
2. Tester le script batch manuellement
3. Contrôler les permissions PHP CLI
4. Vérifier les logs d'erreurs
```

### Problème: Upload impossible
```
Solutions:
1. Vérifier les permissions du dossier library
2. Contrôler upload_max_filesize dans php.ini
3. Vérifier l'espace disque disponible
4. Redémarrer Apache
```

## Validation finale

### ✅ Installation réussie si:
- [ ] Tous les tests de base passent
- [ ] Interface web complètement fonctionnelle
- [ ] Création de contenu possible
- [ ] Tâches automatiques actives
- [ ] Aucune erreur critique dans les logs
- [ ] Performance acceptable (< 3s)

### 🎉 Félicitations !
Votre installation Xibo CMS 4.2.3 est maintenant **complètement opérationnelle** et prête pour la production !

### Prochaines étapes recommandées:
1. **Sauvegarder** l'installation complète
2. **Installer** des players Xibo sur vos écrans
3. **Créer** vos contenus personnalisés
4. **Former** les utilisateurs finaux
5. **Planifier** la maintenance préventive
