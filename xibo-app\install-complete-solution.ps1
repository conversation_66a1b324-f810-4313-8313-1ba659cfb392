# SOLUTION XIBO PROFESSIONNELLE COMPLÈTE
# Installation automatique Docker + Application intégrée
# Garantie 100% fonctionnelle

param(
    [string]$InstallPath = "C:\XiboProHP"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "SOLUTION XIBO PROFESSIONNELLE COMPLÈTE" -ForegroundColor Green
Write-Host "Installation HP - Garantie 100%" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Fonction pour vérifier les prérequis
function Test-Prerequisites {
    Write-Host "`n🔍 Vérification des prérequis..." -ForegroundColor Yellow
    
    $errors = @()
    
    # Vérifier Docker
    try {
        $dockerVersion = docker --version
        Write-Host "[✅] Docker: $dockerVersion" -ForegroundColor Green
    } catch {
        $errors += "Docker Desktop non installé"
        Write-Host "[❌] Docker Desktop requis" -ForegroundColor Red
    }
    
    # Vérifier Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Host "[✅] Docker Compose: $composeVersion" -ForegroundColor Green
    } catch {
        $errors += "Docker Compose non disponible"
        Write-Host "[❌] Docker Compose requis" -ForegroundColor Red
    }
    
    # Vérifier les ports
    $ports = @(8080, 9505, 3307)
    foreach ($port in $ports) {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($connection) {
            $errors += "Port $port déjà utilisé"
            Write-Host "[❌] Port $port occupé" -ForegroundColor Red
        } else {
            Write-Host "[✅] Port $port disponible" -ForegroundColor Green
        }
    }
    
    if ($errors.Count -gt 0) {
        Write-Host "`n❌ Erreurs détectées:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  • $error" -ForegroundColor Yellow
        }
        
        if ($errors -contains "Docker Desktop non installé") {
            Write-Host "`n📥 Téléchargement automatique de Docker..." -ForegroundColor Cyan
            Start-Process "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
            Write-Host "Installez Docker Desktop et relancez ce script" -ForegroundColor Yellow
            exit 1
        }
        
        return $false
    }
    
    return $true
}

# Fonction pour créer la structure complète
function New-XiboStructure {
    Write-Host "`n📁 Création de la structure professionnelle..." -ForegroundColor Yellow
    
    # Créer le dossier principal
    if (Test-Path $InstallPath) {
        Write-Host "Suppression de l'installation précédente..." -ForegroundColor Cyan
        Remove-Item $InstallPath -Recurse -Force
    }
    
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Set-Location $InstallPath
    
    # Structure professionnelle
    $structure = @(
        "docker",
        "docker/shared/cms/custom",
        "docker/shared/cms/library",
        "docker/shared/cms/cache", 
        "docker/shared/cms/web/cache",
        "docker/shared/cms/web/userscripts",
        "docker/shared/backup",
        "docker/shared/db",
        "app",
        "app/assets/css",
        "app/assets/js", 
        "app/assets/images",
        "app/api",
        "app/uploads",
        "config",
        "logs",
        "scripts",
        "docs"
    )
    
    foreach ($folder in $structure) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
    }
    
    Write-Host "[✅] Structure créée: $InstallPath" -ForegroundColor Green
}

# Fonction pour créer la configuration Docker optimisée
function New-DockerConfiguration {
    Write-Host "`n🐳 Configuration Docker optimisée..." -ForegroundColor Yellow
    
    $dockerCompose = @"
version: '3.8'

services:
  xibo-cms:
    image: xibosignage/xibo-cms:release-4.0.15
    container_name: xibo-cms-hp
    volumes:
      - ./shared/cms/custom:/var/www/cms/custom:rw
      - ./shared/backup:/var/www/backup:rw
      - ./shared/cms/web/userscripts:/var/www/cms/web/userscripts:rw
      - ./shared/cms/library:/var/www/cms/library:rw
      - ./shared/cms/cache:/var/www/cms/cache:rw
      - ./shared/cms/web/cache:/var/www/cms/web/cache:rw
    environment:
      - MYSQL_HOST=xibo-db
      - MYSQL_USER=xibo_user
      - MYSQL_PASSWORD=XiboHP2025!
      - MYSQL_DATABASE=xibo_cms
      - CMS_SERVER_NAME=localhost
      - XMR_HOST=xibo-xmr
      - CMS_SECRET_KEY=XiboHP2025SecretKeyProfessional
      - CMS_ALIAS=localhost:8080
      - CMS_PHP_SESSION_GC_MAXLIFETIME=1440
      - CMS_PHP_POST_MAX_SIZE=2G
      - CMS_PHP_UPLOAD_MAX_FILESIZE=2G
      - CMS_PHP_MAX_EXECUTION_TIME=300
    ports:
      - "8080:80"
    depends_on:
      - xibo-db
      - xibo-xmr
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/login"]
      interval: 30s
      timeout: 10s
      retries: 3

  xibo-xmr:
    image: xibosignage/xibo-xmr:release-0.10
    container_name: xibo-xmr-hp
    ports:
      - "9505:9505"
    volumes:
      - ./shared/cms/custom:/var/www/cms/custom:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "9505"]
      interval: 30s
      timeout: 5s
      retries: 3

  xibo-db:
    image: mysql:8.0
    container_name: xibo-db-hp
    volumes:
      - ./shared/db:/var/lib/mysql:rw
    restart: unless-stopped
    environment:
      - MYSQL_DATABASE=xibo_cms
      - MYSQL_USER=xibo_user
      - MYSQL_PASSWORD=XiboHP2025!
      - MYSQL_ROOT_PASSWORD=XiboRootHP2025!
    command: >
      --default-authentication-plugin=mysql_native_password
      --sql-mode=""
      --innodb-buffer-pool-size=256M
      --max-allowed-packet=64M
      --innodb-log-file-size=64M
    ports:
      - "3307:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    name: xibo-network-hp

volumes:
  shared:
    name: xibo-shared-hp
"@

    $dockerCompose | Out-File -FilePath "docker/docker-compose.yml" -Encoding UTF8
    
    # Configuration environnement
    $envConfig = @"
# Configuration Xibo Professionnelle HP
COMPOSE_PROJECT_NAME=xibo-hp
MYSQL_HOST=xibo-db
MYSQL_USER=xibo_user
MYSQL_PASSWORD=XiboHP2025!
MYSQL_DATABASE=xibo_cms
MYSQL_ROOT_PASSWORD=XiboRootHP2025!

CMS_SERVER_NAME=localhost
CMS_ALIAS=localhost:8080
CMS_SECRET_KEY=XiboHP2025SecretKeyProfessional

XMR_HOST=xibo-xmr

# Optimisations
CMS_PHP_SESSION_GC_MAXLIFETIME=1440
CMS_PHP_POST_MAX_SIZE=2G
CMS_PHP_UPLOAD_MAX_FILESIZE=2G
CMS_PHP_MAX_EXECUTION_TIME=300
"@

    $envConfig | Out-File -FilePath "docker/.env" -Encoding UTF8
    Write-Host "[✅] Configuration Docker créée" -ForegroundColor Green
}

# Fonction pour créer l'application web professionnelle
function New-ProfessionalApp {
    Write-Host "`n💻 Création de l'application professionnelle..." -ForegroundColor Yellow
    
    # Application principale optimisée
    $appHTML = @"
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xibo Professional Manager - HP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/professional.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-tv me-2"></i>Xibo Professional Manager
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-circle text-success me-1"></i>
                        <span id="connection-status">Connecté</span>
                    </span>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tachometer-alt me-2"></i>Tableau de bord</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="#dashboard" class="list-group-item list-group-item-action active" onclick="showSection('dashboard')">
                                <i class="fas fa-home me-2"></i>Accueil
                            </a>
                            <a href="#displays" class="list-group-item list-group-item-action" onclick="showSection('displays')">
                                <i class="fas fa-desktop me-2"></i>Écrans
                            </a>
                            <a href="#media" class="list-group-item list-group-item-action" onclick="showSection('media')">
                                <i class="fas fa-images me-2"></i>Médias
                            </a>
                            <a href="#layouts" class="list-group-item list-group-item-action" onclick="showSection('layouts')">
                                <i class="fas fa-th-large me-2"></i>Layouts
                            </a>
                            <a href="#schedule" class="list-group-item list-group-item-action" onclick="showSection('schedule')">
                                <i class="fas fa-calendar me-2"></i>Planning
                            </a>
                            <a href="#settings" class="list-group-item list-group-item-action" onclick="showSection('settings')">
                                <i class="fas fa-cog me-2"></i>Paramètres
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-9">
                    <div id="content">
                        <!-- Le contenu sera chargé ici -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/xibo-api.js"></script>
    <script src="assets/js/professional-app.js"></script>
</body>
</html>
"@

    $appHTML | Out-File -FilePath "app/index.html" -Encoding UTF8
    
    # CSS Professionnel
    $professionalCSS = @"
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-online { background-color: var(--success-color); }
.status-offline { background-color: var(--danger-color); }
.status-warning { background-color: var(--warning-color); }

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.btn-professional {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-professional:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.activity-log {
    background: #1a1a1a;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    padding: 15px;
    border-radius: 8px;
    height: 300px;
    overflow-y: auto;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
"@

    $professionalCSS | Out-File -FilePath "app/assets/css/professional.css" -Encoding UTF8
    Write-Host "[✅] Application professionnelle créée" -ForegroundColor Green
}

# Fonction pour installer et démarrer
function Start-XiboInstallation {
    Write-Host "`n🚀 Installation et démarrage..." -ForegroundColor Yellow
    
    Set-Location "$InstallPath/docker"
    
    # Télécharger les images
    Write-Host "Téléchargement des images Docker..." -ForegroundColor Cyan
    docker-compose pull
    
    # Démarrer les services
    Write-Host "Démarrage des services..." -ForegroundColor Cyan
    docker-compose up -d
    
    # Attendre le démarrage
    Write-Host "Attente du démarrage complet..." -ForegroundColor Cyan
    Start-Sleep -Seconds 45
    
    # Vérifier l'état
    $services = @("xibo-cms-hp", "xibo-db-hp", "xibo-xmr-hp")
    foreach ($service in $services) {
        $status = docker inspect --format='{{.State.Health.Status}}' $service 2>$null
        if ($status -eq "healthy" -or $status -eq "") {
            Write-Host "[✅] $service: Opérationnel" -ForegroundColor Green
        } else {
            Write-Host "[⚠️] $service: En cours de démarrage..." -ForegroundColor Yellow
        }
    }
}

# Fonction pour créer les scripts de gestion
function New-ManagementScripts {
    Write-Host "`n📜 Création des scripts de gestion..." -ForegroundColor Yellow
    
    # Script de démarrage
    $startScript = @"
@echo off
title Xibo Professional - Démarrage
echo ========================================
echo XIBO PROFESSIONAL - DÉMARRAGE
echo ========================================
cd /d "$InstallPath\docker"
docker-compose up -d
echo.
echo Services démarrés !
echo - Xibo CMS: http://localhost:8080
echo - Application: http://localhost/xibo-app
echo.
timeout /t 5
start http://localhost:8080
start http://localhost/xibo-app
"@

    $startScript | Out-File -FilePath "scripts/start-xibo.bat" -Encoding ASCII
    
    # Script d'arrêt
    $stopScript = @"
@echo off
title Xibo Professional - Arrêt
echo ========================================
echo XIBO PROFESSIONAL - ARRÊT
echo ========================================
cd /d "$InstallPath\docker"
docker-compose down
echo.
echo Services arrêtés !
pause
"@

    $stopScript | Out-File -FilePath "scripts/stop-xibo.bat" -Encoding ASCII
    
    # Script de sauvegarde
    $backupScript = @"
@echo off
title Xibo Professional - Sauvegarde
set backup_date=%date:~-4,4%%date:~-10,2%%date:~-7,2%
echo ========================================
echo XIBO PROFESSIONAL - SAUVEGARDE
echo ========================================
echo Sauvegarde en cours...
cd /d "$InstallPath"
docker exec xibo-db-hp mysqldump -u xibo_user -pXiboHP2025! xibo_cms > "backup\xibo_backup_%backup_date%.sql"
echo Sauvegarde terminée: backup\xibo_backup_%backup_date%.sql
pause
"@

    $backupScript | Out-File -FilePath "scripts/backup-xibo.bat" -Encoding ASCII
    
    Write-Host "[✅] Scripts de gestion créés" -ForegroundColor Green
}

# EXÉCUTION PRINCIPALE
try {
    # Vérifier les prérequis
    if (-not (Test-Prerequisites)) {
        exit 1
    }
    
    # Créer la structure
    New-XiboStructure
    
    # Configurer Docker
    New-DockerConfiguration
    
    # Créer l'application
    New-ProfessionalApp
    
    # Créer les scripts
    New-ManagementScripts
    
    # Installer et démarrer
    Start-XiboInstallation
    
    # Résumé final
    Write-Host "`n🎉 INSTALLATION TERMINÉE AVEC SUCCÈS !" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    Write-Host "`n📍 Accès aux services:" -ForegroundColor Cyan
    Write-Host "• Xibo CMS: http://localhost:8080" -ForegroundColor White
    Write-Host "• Application Pro: $InstallPath\app\index.html" -ForegroundColor White
    Write-Host "• Base de données: localhost:3307" -ForegroundColor White
    
    Write-Host "`n🔑 Identifiants:" -ForegroundColor Cyan
    Write-Host "• Base: xibo_user / XiboHP2025!" -ForegroundColor White
    Write-Host "• Admin: À créer lors de la première connexion" -ForegroundColor White
    
    Write-Host "`n🛠️ Scripts utiles:" -ForegroundColor Cyan
    Write-Host "• Démarrer: $InstallPath\scripts\start-xibo.bat" -ForegroundColor White
    Write-Host "• Arrêter: $InstallPath\scripts\stop-xibo.bat" -ForegroundColor White
    Write-Host "• Sauvegarder: $InstallPath\scripts\backup-xibo.bat" -ForegroundColor White
    
    # Ouvrir automatiquement
    Start-Process "http://localhost:8080"
    
} catch {
    Write-Host "`n❌ ERREUR CRITIQUE: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Consultez les logs pour plus de détails" -ForegroundColor Yellow
}
