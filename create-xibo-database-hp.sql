-- Script SQL pour créer la base de données Xibo CMS
-- Spécifique pour l'installation de HP avec Xibo 4.2.3
-- À exécuter dans phpMyAdmin (http://localhost/phpmyadmin)

-- =====================================================
-- INFORMATIONS D'INSTALLATION
-- =====================================================
/*
Installation: Xibo CMS 4.2.3
Utilisateur: HP
Chemin source: C:\Users\<USER>\Downloads\Prodynamique\xibo-cms-4.2.3
Chemin destination: C:\xampp\htdocs\xibo
Date: 2025-01-07
*/

-- =====================================================
-- SUPPRESSION ET CRÉATION DE LA BASE DE DONNÉES
-- =====================================================

-- Supprimer la base si elle existe (ATTENTION: supprime toutes les données)
DROP DATABASE IF EXISTS xibo_cms;

-- Créer la base de données avec le bon charset pour Xibo 4.2.3
CREATE DATABASE xibo_cms 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Vérifier la création
SELECT SCHEMA_NAME as 'Base de données créée'
FROM INFORMATION_SCHEMA.SCHEMATA 
WHERE SCHEMA_NAME = 'xibo_cms';

-- =====================================================
-- GESTION DE L'UTILISATEUR XIBO
-- =====================================================

-- Supprimer l'utilisateur s'il existe déjà
DROP USER IF EXISTS 'xibo_user'@'localhost';

-- Créer l'utilisateur avec un mot de passe sécurisé
-- IMPORTANT: Notez bien ce mot de passe pour l'installation web
CREATE USER 'xibo_user'@'localhost' IDENTIFIED BY 'XiboHP2025!';

-- Accorder tous les privilèges sur la base xibo_cms
GRANT ALL PRIVILEGES ON xibo_cms.* TO 'xibo_user'@'localhost';

-- Privilèges spécifiques pour une sécurité renforcée (alternative)
-- GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, 
-- CREATE TEMPORARY TABLES, LOCK TABLES, CREATE VIEW, SHOW VIEW, 
-- CREATE ROUTINE, ALTER ROUTINE, EXECUTE, EVENT, TRIGGER 
-- ON xibo_cms.* TO 'xibo_user'@'localhost';

-- Appliquer les changements
FLUSH PRIVILEGES;

-- =====================================================
-- VÉRIFICATIONS DE LA CONFIGURATION
-- =====================================================

-- Vérifier que la base de données existe
SELECT 
    SCHEMA_NAME as 'Base de données',
    DEFAULT_CHARACTER_SET_NAME as 'Charset',
    DEFAULT_COLLATION_NAME as 'Collation'
FROM INFORMATION_SCHEMA.SCHEMATA 
WHERE SCHEMA_NAME = 'xibo_cms';

-- Vérifier que l'utilisateur existe
SELECT 
    User as 'Utilisateur', 
    Host as 'Hôte',
    plugin as 'Méthode auth'
FROM mysql.user 
WHERE User = 'xibo_user';

-- Vérifier les privilèges accordés
SHOW GRANTS FOR 'xibo_user'@'localhost';

-- =====================================================
-- CONFIGURATION MYSQL POUR XIBO 4.2.3
-- =====================================================

-- Vérifier la configuration MySQL actuelle
SELECT 
    @@sql_mode as 'SQL Mode actuel',
    @@max_allowed_packet as 'Max packet size (bytes)',
    @@innodb_buffer_pool_size as 'InnoDB buffer pool (bytes)',
    @@character_set_server as 'Charset serveur',
    @@collation_server as 'Collation serveur';

-- Recommandations pour optimiser MySQL pour Xibo
-- À ajouter dans my.ini (section [mysqld]):
/*
[mysqld]
sql_mode = ""
max_allowed_packet = 64M
innodb_buffer_pool_size = 256M
innodb_file_per_table = 1
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
innodb_default_row_format = dynamic
*/

-- =====================================================
-- TEST DE CONNEXION
-- =====================================================

-- Sélectionner la base de données
USE xibo_cms;

-- Créer une table de test pour vérifier les permissions
CREATE TABLE IF NOT EXISTS test_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    test_message VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insérer des données de test
INSERT INTO test_permissions (test_message) VALUES 
('Test de connexion réussi'),
('Permissions d\'écriture OK'),
('Charset UTF8MB4 fonctionnel: éàèùç 🎉');

-- Vérifier les données
SELECT * FROM test_permissions;

-- Supprimer la table de test
DROP TABLE test_permissions;

-- =====================================================
-- INFORMATIONS POUR L'INSTALLATION WEB
-- =====================================================

SELECT 'Configuration terminée avec succès!' as 'Statut';

/*
=== INFORMATIONS POUR L'INSTALLATION WEB XIBO ===

URL d'installation: http://localhost/xibo ou http://xibo.local

Paramètres de base de données à utiliser:
- Host de base de données: localhost
- Nom de la base de données: xibo_cms
- Utilisateur de base de données: xibo_user
- Mot de passe: XiboHP2025!

Paramètres système:
- Dossier Library: C:\xampp\htdocs\xibo\library
- Version Xibo: 4.2.3
- PHP requis: 8.1+
- MySQL: 5.7+

Prochaines étapes:
1. Vérifier que Apache et MySQL sont démarrés dans XAMPP
2. Accéder à http://localhost/xibo/test-permissions.php
3. Lancer l'installation web: http://localhost/xibo
4. Utiliser les paramètres de base de données ci-dessus
5. Configurer les tâches automatiques (XTR)

Dossiers importants:
- Installation: C:\xampp\htdocs\xibo
- Médias: C:\xampp\htdocs\xibo\library
- Cache: C:\xampp\htdocs\xibo\cache
- Logs: C:\xampp\htdocs\xibo\library\log

Sécurité:
- Changez le mot de passe 'XiboHP2025!' si nécessaire
- Notez les identifiants administrateur lors de l'installation web
- Configurez des sauvegardes régulières
*/

-- =====================================================
-- SCRIPT DE SAUVEGARDE (pour référence future)
-- =====================================================

/*
Pour sauvegarder la base de données après installation:

Via ligne de commande:
mysqldump -u xibo_user -p xibo_cms > xibo_backup_YYYYMMDD.sql

Via phpMyAdmin:
1. Sélectionner la base 'xibo_cms'
2. Onglet 'Exporter'
3. Choisir 'Méthode rapide' et format 'SQL'
4. Cliquer 'Exécuter'

Pour restaurer:
mysql -u xibo_user -p xibo_cms < xibo_backup_YYYYMMDD.sql
*/

-- =====================================================
-- NETTOYAGE D'URGENCE (si problème)
-- =====================================================

/*
En cas de problème, pour tout supprimer et recommencer:

DROP DATABASE IF EXISTS xibo_cms;
DROP USER IF EXISTS 'xibo_user'@'localhost';
FLUSH PRIVILEGES;

Puis relancer ce script.
*/

-- Fin du script
SELECT 
    'Base de données Xibo créée avec succès!' as 'Message',
    'Vous pouvez maintenant procéder à l\'installation web' as 'Prochaine étape',
    'http://localhost/xibo' as 'URL d\'installation';
