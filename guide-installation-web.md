# Guide d'installation web Xibo CMS

## Prérequis avant l'installation web

### ✅ Vérifications nécessaires
- [ ] XAMPP installé et fonctionnel
- [ ] Apache et MySQL démarrés
- [ ] Archive Xibo extraite dans `C:\xampp\htdocs\xibo\`
- [ ] Base de données `xibo_cms` créée
- [ ] Utilisateur `xibo_user` configuré
- [ ] Permissions des dossiers configurées
- [ ] Virtual Host configuré (optionnel)

## Étape 1: Accéder à l'installateur

### URL d'accès
- **Avec Virtual Host**: http://xibo.local
- **Sans Virtual Host**: http://localhost/xibo

### Page d'accueil
L'installateur Xibo se lance automatiquement si aucune configuration n'existe.

## Étape 2: Vérification des prérequis

### Écran "Prerequisites Check"
L'installateur vérifie automatiquement:

#### ✅ Éléments qui doivent être verts:
- **PHP Version**: 8.1+ requis
- **PHP Extensions**: Toutes les extensions listées
- **File Permissions**: Dossiers library et cache
- **Database**: Connexion MySQL possible
- **Rewrite**: URL rewriting activé

#### ⚠️ Problèmes courants:
| Problème | Solution |
|----------|----------|
| PHP Version trop ancienne | Mettre à jour XAMPP |
| Extension manquante | Activer dans php.ini |
| Permissions insuffisantes | Exécuter configure-permissions.bat |
| mod_rewrite désactivé | Activer dans httpd.conf |

### Actions si problèmes:
1. Corriger les problèmes identifiés
2. Cliquer sur "Retest" 
3. Continuer quand tout est vert

## Étape 3: Configuration de la base de données

### Onglet "New Database" (recommandé)
- **Database Host**: `localhost`
- **Admin Username**: `root` (utilisateur MySQL admin)
- **Admin Password**: (mot de passe root MySQL)
- **Database Name**: `xibo_cms`
- **Database Username**: `xibo_user`
- **Database Password**: `XiboSecurePass2024!` (ou votre mot de passe)

### Onglet "Existing Database" (si base déjà créée)
- **Database Host**: `localhost`
- **Database Name**: `xibo_cms`
- **Database Username**: `xibo_user`
- **Database Password**: `XiboSecurePass2024!`

### Test de connexion
L'installateur teste automatiquement la connexion avant de continuer.

## Étape 4: Installation de la base de données

### Processus automatique
- Création des tables Xibo
- Insertion des données par défaut
- Configuration des index
- **Durée**: 2-5 minutes selon la performance

### Indicateurs de progression
- Points qui apparaissent progressivement
- Messages de statut
- Barre de progression (selon la version)

## Étape 5: Création du compte administrateur

### Informations requises
- **Username**: `admin` (ou votre choix)
- **Password**: Mot de passe sécurisé
- **Email**: Votre adresse email

### Recommandations sécurité
- Mot de passe de 12+ caractères
- Mélange majuscules/minuscules/chiffres/symboles
- Éviter les mots du dictionnaire
- Noter le mot de passe en lieu sûr

## Étape 6: Configuration du système

### Paramètres principaux

#### Library Location
```
C:\xampp\htdocs\xibo\library
```
**Important**: Utiliser des barres obliques normales `/` même sur Windows

#### CMS Secret Key
- Clé de sécurité pour l'authentification
- Générée automatiquement (recommandé)
- Ou saisir une clé personnalisée (32+ caractères)

#### Statistics
- [ ] Envoyer des statistiques anonymes au projet Xibo
- Optionnel, aide au développement du projet

### Paramètres avancés (optionnels)
- **Timezone**: Europe/Paris
- **Language**: Français (si disponible)
- **Date Format**: Format européen

## Étape 7: Finalisation

### Installation terminée
- Message de confirmation
- Lien vers l'interface d'administration
- Informations de connexion rappelées

### Première connexion
1. Cliquer sur "Login to Xibo"
2. Saisir les identifiants administrateur
3. Accéder au tableau de bord

## Post-installation immédiate

### Vérifications essentielles

#### 1. Tableau de bord accessible
- URL: http://xibo.local ou http://localhost/xibo
- Connexion avec compte admin
- Aucune erreur affichée

#### 2. Upload de test
- Menu "Library" → "Add Media"
- Tester l'upload d'une image
- Vérifier le stockage dans le dossier library

#### 3. Logs système
- Menu "Administration" → "Log"
- Vérifier l'absence d'erreurs critiques

### Configuration initiale recommandée

#### 1. Paramètres régionaux
- Administration → Settings → Regional
- Timezone: Europe/Paris
- Language: Français
- Date/Time format: Format local

#### 2. Paramètres d'affichage
- Administration → Settings → Displays
- Configure selon vos besoins

#### 3. Première mise en page
- Layouts → Add Layout
- Créer un layout de test simple

## Dépannage installation web

### Erreur "Database Connection Failed"
```
Solutions:
1. Vérifier que MySQL est démarré
2. Contrôler les identifiants de base
3. Tester la connexion manuellement
4. Vérifier les permissions utilisateur
```

### Erreur "Library not writable"
```
Solutions:
1. Exécuter configure-permissions.bat en admin
2. Vérifier le chemin du dossier library
3. Contrôler les permissions Windows
4. Redémarrer Apache
```

### Erreur "URL Rewriting not working"
```
Solutions:
1. Activer mod_rewrite dans httpd.conf
2. Vérifier le fichier .htaccess
3. Contrôler la configuration Virtual Host
4. Redémarrer Apache
```

### Page blanche ou erreur 500
```
Solutions:
1. Consulter les logs Apache (error.log)
2. Vérifier les logs PHP
3. Contrôler les permissions des fichiers
4. Vérifier la configuration PHP
```

### Timeout pendant l'installation
```
Solutions:
1. Augmenter max_execution_time dans php.ini
2. Augmenter memory_limit
3. Vérifier la performance MySQL
4. Redémarrer l'installation
```

## Fichiers de configuration créés

### Après installation réussie
- `web/settings.php` - Configuration principale
- `library/certs/` - Certificats de sécurité
- Base de données peuplée avec tables Xibo

### Sauvegarde recommandée
```batch
# Sauvegarder immédiatement après installation
mysqldump -u xibo_user -p xibo_cms > xibo_fresh_install.sql
xcopy "C:\xampp\htdocs\xibo" "C:\backup\xibo_fresh\" /E /I
```

## Prochaines étapes

1. **Configurer les tâches automatiques** (XTR)
2. **Installer et configurer un player Xibo**
3. **Créer vos premiers contenus**
4. **Configurer la sécurité avancée**
5. **Planifier les sauvegardes automatiques**

---

**Support**: En cas de problème, consulter les logs et la documentation officielle Xibo.
