# Guide d'installation Xibo avec XAMPP

## Étape 1: Télécharger et extraire Xibo CMS

### 1.1 Téléchargement
- Allez sur: https://github.com/xibosignage/xibo-cms/releases
- Téléchargez la dernière version stable (ex: xibo-cms-4.0.x.zip)

### 1.2 Extraction
```
# Extraire dans le dossier XAMPP
C:\xampp\htdocs\xibo\
```

**Structure recommandée:**
```
C:\xampp\htdocs\xibo\
├── web/           (dossier accessible par le navigateur)
├── lib/
├── cache/
├── library/       (stockage des médias)
└── vendor/
```

## Étape 2: Configuration XAMPP

### 2.1 Configuration PHP (php.ini)
Ouvrir: `C:\xampp\php\php.ini`

```ini
# Extensions requises (décommenter si nécessaire)
extension=gd
extension=pdo_mysql
extension=zip
extension=curl
extension=soap
extension=mbstring
extension=fileinfo

# Paramètres recommandés
memory_limit = 256M
upload_max_filesize = 128M
post_max_size = 128M
max_execution_time = 300
```

### 2.2 Configuration Apache (.htaccess)
Le fichier `.htaccess` est fourni dans `web/.htaccess`

### 2.3 Virtual Host (Optionnel mais recommandé)
Éditer: `C:\xampp\apache\conf\extra\httpd-vhosts.conf`

```apache
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/xibo/web"
    ServerName xibo.local
    
    <Directory "C:/xampp/htdocs/xibo/web">
        AllowOverride All
        Options Indexes FollowSymLinks MultiViews
        Require all granted
    </Directory>
</VirtualHost>
```

Ajouter dans `C:\Windows\System32\drivers\etc\hosts`:
```
127.0.0.1 xibo.local
```

## Étape 3: Base de données MySQL

### 3.1 Créer la base de données
Via phpMyAdmin (http://localhost/phpmyadmin):

```sql
CREATE DATABASE xibo_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xibo_user'@'localhost' IDENTIFIED BY 'votre_mot_de_passe';
GRANT ALL PRIVILEGES ON xibo_cms.* TO 'xibo_user'@'localhost';
FLUSH PRIVILEGES;
```

## Étape 4: Permissions et dossiers

### 4.1 Créer les dossiers nécessaires
```
C:\xampp\htdocs\xibo\library\
C:\xampp\htdocs\xibo\cache\
C:\xampp\htdocs\xibo\library\certs\
```

### 4.2 Permissions Windows
- Clic droit sur le dossier `xibo`
- Propriétés → Sécurité
- Donner contrôle total à l'utilisateur IUSR et IIS_IUSRS

## Étape 5: Installation web

### 5.1 Accéder à l'installateur
Navigateur: `http://localhost/xibo` ou `http://xibo.local`

### 5.2 Suivre l'assistant
1. **Prérequis**: Vérifier que tous les éléments sont verts
2. **Base de données**: 
   - Host: localhost
   - Database: xibo_cms
   - Username: xibo_user
   - Password: votre_mot_de_passe
3. **Admin**: Créer le compte administrateur
4. **Configuration**:
   - Library: `C:\xampp\htdocs\xibo\library`
   - CMS Key: générer une clé sécurisée

## Étape 6: Configuration post-installation

### 6.1 Générer les clés de sécurité
Créer le fichier: `C:\xampp\htdocs\xibo\library\certs\private.key`
Créer le fichier: `C:\xampp\htdocs\xibo\library\certs\public.key`

### 6.2 Tâches automatiques (XTR)
Créer une tâche planifiée Windows:

```batch
# Commande à exécuter chaque minute
C:\xampp\php\php.exe C:\xampp\htdocs\xibo\bin\xtr.php
```

**Configuration Planificateur de tâches:**
1. Ouvrir "Planificateur de tâches"
2. Créer une tâche de base
3. Nom: "Xibo Maintenance"
4. Déclencheur: Quotidien, répéter toutes les 1 minute
5. Action: Démarrer un programme
6. Programme: `C:\xampp\php\php.exe`
7. Arguments: `C:\xampp\htdocs\xibo\bin\xtr.php`

## Étape 7: Test de l'installation

### 7.1 Connexion
- URL: `http://localhost/xibo` ou `http://xibo.local`
- Utilisateur: admin (ou celui créé)
- Mot de passe: celui défini lors de l'installation

### 7.2 Vérifications
1. Tableau de bord accessible
2. Aucune erreur dans les logs
3. Possibilité d'uploader des médias
4. Création d'un layout de test

## Dépannage courant

### Erreur "Library not writable"
- Vérifier les permissions du dossier library
- S'assurer que Apache peut écrire dans ce dossier

### Erreur de base de données
- Vérifier les identifiants MySQL
- S'assurer que MySQL est démarré dans XAMPP

### Erreur 404 ou URL rewriting
- Vérifier que mod_rewrite est activé dans Apache
- Contrôler le fichier .htaccess

### Performance lente
- Augmenter memory_limit dans php.ini
- Optimiser la configuration MySQL

## Ressources supplémentaires

- Documentation officielle: https://xibosignage.com/docs
- Forum communautaire: https://community.xibo.org.uk
- GitHub: https://github.com/xibosignage/xibo-cms
