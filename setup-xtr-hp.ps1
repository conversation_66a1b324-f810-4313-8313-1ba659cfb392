# Script PowerShell pour configurer les tâches automatiques Xibo (XTR)
# Spécifique pour l'installation HP avec Xibo 4.2.3
# À exécuter en tant qu'administrateur

param(
    [string]$XamppPath = "C:\xampp",
    [string]$XiboPath = "C:\xampp\htdocs\xibo"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Configuration des tâches automatiques Xibo (XTR)" -ForegroundColor Green
Write-Host "Installation HP - Xibo CMS 4.2.3" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Vérifier les prérequis
Write-Host "`nVérification des prérequis..." -ForegroundColor Yellow

if (-not (Test-Path "$XamppPath\php\php.exe")) {
    Write-Host "ERREUR: PHP non trouvé dans $XamppPath" -ForegroundColor Red
    Write-Host "Veuillez vérifier l'installation XAMPP" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Path "$XiboPath\bin\xtr.php")) {
    Write-Host "ERREUR: XTR non trouvé dans $XiboPath" -ForegroundColor Red
    Write-Host "Veuillez d'abord terminer l'installation web de Xibo" -ForegroundColor Yellow
    exit 1
}

Write-Host "[OK] PHP trouvé: $XamppPath\php\php.exe" -ForegroundColor Green
Write-Host "[OK] XTR trouvé: $XiboPath\bin\xtr.php" -ForegroundColor Green

# Tester PHP CLI
Write-Host "`nTest de PHP CLI..." -ForegroundColor Yellow
try {
    $phpVersion = & "$XamppPath\php\php.exe" --version
    Write-Host "[OK] PHP CLI fonctionnel" -ForegroundColor Green
    Write-Host "Version: $($phpVersion.Split("`n")[0])" -ForegroundColor Cyan
} catch {
    Write-Host "ERREUR: PHP CLI non fonctionnel" -ForegroundColor Red
    exit 1
}

# Créer le script batch pour XTR
Write-Host "`nCréation du script de maintenance..." -ForegroundColor Yellow
$batchScript = @"
@echo off
REM Script de maintenance Xibo XTR
REM Créé automatiquement pour l'installation HP
REM Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

REM Définir les chemins
set XIBO_PATH=$XiboPath
set PHP_PATH=$XamppPath\php\php.exe
set LOG_FILE=%XIBO_PATH%\library\log\xtr-maintenance.log

REM Créer le dossier log s'il n'existe pas
if not exist "%XIBO_PATH%\library\log" mkdir "%XIBO_PATH%\library\log"

REM Log de début
echo %date% %time% - Début maintenance XTR >> "%LOG_FILE%"

REM Changer vers le dossier Xibo
cd /d "%XIBO_PATH%"

REM Exécuter XTR avec gestion d'erreurs
"%PHP_PATH%" "%XIBO_PATH%\bin\xtr.php" >> "%LOG_FILE%" 2>&1

REM Vérifier le code de retour
if %ERRORLEVEL% EQU 0 (
    echo %date% %time% - XTR terminé avec succès >> "%LOG_FILE%"
) else (
    echo %date% %time% - XTR terminé avec erreur %ERRORLEVEL% >> "%LOG_FILE%"
)

REM Nettoyer les logs anciens (garder 30 jours)
forfiles /p "%XIBO_PATH%\library\log" /s /m *.log /d -30 /c "cmd /c del @path" 2>nul

REM Log de fin
echo %date% %time% - Fin maintenance XTR >> "%LOG_FILE%"
"@

$batchPath = "$XiboPath\xibo-xtr-maintenance.bat"
$batchScript | Set-Content $batchPath -Encoding ASCII
Write-Host "[CRÉÉ] Script batch: $batchPath" -ForegroundColor Green

# Tester le script batch
Write-Host "`nTest du script de maintenance..." -ForegroundColor Yellow
try {
    & $batchPath
    Write-Host "[OK] Script de maintenance testé avec succès" -ForegroundColor Green
} catch {
    Write-Host "[ATTENTION] Erreur lors du test du script" -ForegroundColor Yellow
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Créer la tâche planifiée
Write-Host "`nCréation de la tâche planifiée..." -ForegroundColor Yellow

# Supprimer la tâche existante si elle existe
$taskName = "Xibo Maintenance XTR (HP)"
try {
    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
    Write-Host "[INFO] Tâche existante supprimée" -ForegroundColor Cyan
} catch {
    # Pas de problème si la tâche n'existait pas
}

# Créer les composants de la tâche
$action = New-ScheduledTaskAction -Execute $batchPath
$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 1) -RepetitionDuration (New-TimeSpan -Days 365)
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -ExecutionTimeLimit (New-TimeSpan -Minutes 10)
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

# Enregistrer la tâche
try {
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "Tâche de maintenance automatique Xibo CMS - Installation HP"
    Write-Host "[OK] Tâche planifiée créée: $taskName" -ForegroundColor Green
} catch {
    Write-Host "ERREUR lors de la création de la tâche planifiée" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    
    # Méthode alternative avec schtasks
    Write-Host "`nTentative avec schtasks..." -ForegroundColor Yellow
    $schtasksCmd = "schtasks /create /tn `"$taskName`" /tr `"$batchPath`" /sc minute /mo 1 /ru SYSTEM /f"
    try {
        Invoke-Expression $schtasksCmd
        Write-Host "[OK] Tâche créée avec schtasks" -ForegroundColor Green
    } catch {
        Write-Host "ERREUR: Impossible de créer la tâche automatiquement" -ForegroundColor Red
        Write-Host "Vous devrez la créer manuellement" -ForegroundColor Yellow
    }
}

# Vérifier la tâche
Write-Host "`nVérification de la tâche..." -ForegroundColor Yellow
try {
    $task = Get-ScheduledTask -TaskName $taskName -ErrorAction Stop
    Write-Host "[OK] Tâche trouvée: $($task.TaskName)" -ForegroundColor Green
    Write-Host "État: $($task.State)" -ForegroundColor Cyan
    
    # Tester l'exécution
    Write-Host "`nTest d'exécution de la tâche..." -ForegroundColor Yellow
    Start-ScheduledTask -TaskName $taskName
    Start-Sleep -Seconds 5
    
    $taskInfo = Get-ScheduledTask -TaskName $taskName
    Write-Host "État après test: $($taskInfo.State)" -ForegroundColor Cyan
    
} catch {
    Write-Host "[ATTENTION] Impossible de vérifier la tâche automatiquement" -ForegroundColor Yellow
}

# Créer un script de surveillance
Write-Host "`nCréation du script de surveillance..." -ForegroundColor Yellow
$monitorScript = @"
# Script PowerShell de surveillance XTR
# Vérifie que les tâches XTR s'exécutent correctement

param([int]`$Hours = 2)

`$logFile = "$XiboPath\library\log\xtr-maintenance.log"
`$taskName = "$taskName"

Write-Host "Surveillance XTR - Dernières `$Hours heures" -ForegroundColor Green

# Vérifier le fichier de log
if (Test-Path `$logFile) {
    `$cutoff = (Get-Date).AddHours(-`$Hours)
    `$recentLogs = Get-Content `$logFile | Where-Object { 
        `$_ -match "\d{2}/\d{2}/\d{4}" -and 
        [DateTime]::ParseExact(`$_.Substring(0,19), "dd/MM/yyyy HH:mm:ss", `$null) -gt `$cutoff 
    }
    
    if (`$recentLogs) {
        Write-Host "`n[OK] Activité XTR détectée dans les dernières `$Hours heures" -ForegroundColor Green
        Write-Host "Dernières entrées:" -ForegroundColor Cyan
        `$recentLogs | Select-Object -Last 5 | ForEach-Object { Write-Host "  `$_" }
    } else {
        Write-Host "`n[ATTENTION] Aucune activité XTR dans les dernières `$Hours heures" -ForegroundColor Yellow
    }
} else {
    Write-Host "`n[ERREUR] Fichier de log non trouvé: `$logFile" -ForegroundColor Red
}

# Vérifier la tâche planifiée
try {
    `$task = Get-ScheduledTask -TaskName `$taskName
    Write-Host "`n[OK] Tâche planifiée active: `$(`$task.State)" -ForegroundColor Green
} catch {
    Write-Host "`n[ERREUR] Tâche planifiée non trouvée" -ForegroundColor Red
}
"@

$monitorPath = "$XiboPath\monitor-xtr.ps1"
$monitorScript | Set-Content $monitorPath -Encoding UTF8
Write-Host "[CRÉÉ] Script de surveillance: $monitorPath" -ForegroundColor Green

# Créer un raccourci pour la surveillance
$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutPath = "$desktopPath\Surveiller Xibo XTR.lnk"
try {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = "powershell.exe"
    $Shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$monitorPath`""
    $Shortcut.WorkingDirectory = $XiboPath
    $Shortcut.Description = "Surveiller les tâches automatiques Xibo XTR"
    $Shortcut.Save()
    Write-Host "[CRÉÉ] Raccourci bureau: $shortcutPath" -ForegroundColor Green
} catch {
    Write-Host "[INFO] Impossible de créer le raccourci bureau" -ForegroundColor Cyan
}

# Résumé final
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "CONFIGURATION XTR TERMINÉE" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nÉléments configurés:" -ForegroundColor Cyan
Write-Host "✓ Script de maintenance: $batchPath" -ForegroundColor White
Write-Host "✓ Tâche planifiée: $taskName" -ForegroundColor White
Write-Host "✓ Script de surveillance: $monitorPath" -ForegroundColor White
Write-Host "✓ Logs: $XiboPath\library\log\xtr-maintenance.log" -ForegroundColor White

Write-Host "`nFréquence d'exécution: Toutes les minutes" -ForegroundColor Cyan
Write-Host "Utilisateur: SYSTEM (privilèges élevés)" -ForegroundColor Cyan
Write-Host "Timeout: 10 minutes maximum par exécution" -ForegroundColor Cyan

Write-Host "`nCommandes utiles:" -ForegroundColor Yellow
Write-Host "• Surveiller XTR: PowerShell -File `"$monitorPath`"" -ForegroundColor White
Write-Host "• Tester manuellement: `"$batchPath`"" -ForegroundColor White
Write-Host "• Voir les logs: Get-Content `"$XiboPath\library\log\xtr-maintenance.log`"" -ForegroundColor White

Write-Host "`nProchaines étapes:" -ForegroundColor Yellow
Write-Host "1. Vérifier que Xibo CMS est complètement installé" -ForegroundColor White
Write-Host "2. Surveiller les logs pendant 24h" -ForegroundColor White
Write-Host "3. Configurer les sauvegardes automatiques" -ForegroundColor White
Write-Host "4. Tester l'affichage avec des players" -ForegroundColor White

Write-Host "`n[INFO] Configuration terminée avec succès!" -ForegroundColor Green
