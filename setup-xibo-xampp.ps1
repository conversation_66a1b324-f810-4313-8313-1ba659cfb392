# Script PowerShell pour installer Xibo avec XAMPP
# Exécuter en tant qu'administrateur

param(
    [string]$XamppPath = "C:\xampp",
    [string]$XiboVersion = "latest"
)

Write-Host "=== Installation automatique de Xibo avec XAMPP ===" -ForegroundColor Green

# Vérifier si XAMPP est installé
if (-not (Test-Path $XamppPath)) {
    Write-Host "ERREUR: XAMPP n'est pas trouvé dans $XamppPath" -ForegroundColor Red
    Write-Host "Veuillez installer XAMPP d'abord depuis https://www.apachefriends.org/" -ForegroundColor Yellow
    exit 1
}

Write-Host "XAMPP trouvé dans $XamppPath" -ForegroundColor Green

# Définir les chemins
$XiboPath = "$XamppPath\htdocs\xibo"
$LibraryPath = "$XiboPath\library"
$CachePath = "$XiboPath\cache"
$CertsPath = "$LibraryPath\certs"

# Créer les dossiers nécessaires
Write-Host "Création des dossiers..." -ForegroundColor Yellow
$folders = @($XiboPath, $LibraryPath, $CachePath, $CertsPath)
foreach ($folder in $folders) {
    if (-not (Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
        Write-Host "Créé: $folder" -ForegroundColor Green
    }
}

# Télécharger Xibo CMS
Write-Host "Téléchargement de Xibo CMS..." -ForegroundColor Yellow
$downloadUrl = "https://github.com/xibosignage/xibo-cms/releases/latest/download/xibo-cms.tar.gz"
$downloadPath = "$env:TEMP\xibo-cms.tar.gz"

try {
    Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
    Write-Host "Téléchargement terminé" -ForegroundColor Green
} catch {
    Write-Host "ERREUR lors du téléchargement: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Veuillez télécharger manuellement depuis: https://github.com/xibosignage/xibo-cms/releases" -ForegroundColor Yellow
}

# Configuration PHP.ini
Write-Host "Configuration de PHP..." -ForegroundColor Yellow
$phpIniPath = "$XamppPath\php\php.ini"
if (Test-Path $phpIniPath) {
    $phpIniContent = Get-Content $phpIniPath
    
    # Vérifier et modifier les paramètres PHP
    $modifications = @{
        "memory_limit" = "256M"
        "upload_max_filesize" = "128M"
        "post_max_size" = "128M"
        "max_execution_time" = "300"
    }
    
    $modified = $false
    foreach ($setting in $modifications.Keys) {
        $pattern = "^;?$setting\s*="
        $newValue = "$setting = $($modifications[$setting])"
        
        if ($phpIniContent -match $pattern) {
            $phpIniContent = $phpIniContent -replace $pattern, $newValue
            $modified = $true
        }
    }
    
    if ($modified) {
        $phpIniContent | Set-Content $phpIniPath
        Write-Host "PHP.ini configuré" -ForegroundColor Green
    }
}

# Créer le Virtual Host
Write-Host "Configuration du Virtual Host..." -ForegroundColor Yellow
$vhostPath = "$XamppPath\apache\conf\extra\httpd-vhosts.conf"
$vhostConfig = @"

# Xibo Virtual Host
<VirtualHost *:80>
    DocumentRoot "$($XiboPath.Replace('\', '/'))/web"
    ServerName xibo.local
    
    <Directory "$($XiboPath.Replace('\', '/'))/web">
        AllowOverride All
        Options Indexes FollowSymLinks MultiViews
        Require all granted
    </Directory>
</VirtualHost>
"@

if (Test-Path $vhostPath) {
    Add-Content -Path $vhostPath -Value $vhostConfig
    Write-Host "Virtual Host ajouté" -ForegroundColor Green
}

# Modifier le fichier hosts
Write-Host "Configuration du fichier hosts..." -ForegroundColor Yellow
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"
$hostsEntry = "127.0.0.1 xibo.local"

$hostsContent = Get-Content $hostsPath
if ($hostsContent -notcontains $hostsEntry) {
    Add-Content -Path $hostsPath -Value $hostsEntry
    Write-Host "Entrée hosts ajoutée" -ForegroundColor Green
}

# Créer un script de base de données
Write-Host "Création du script de base de données..." -ForegroundColor Yellow
$dbScript = @"
-- Script SQL pour créer la base de données Xibo
-- À exécuter dans phpMyAdmin ou MySQL CLI

CREATE DATABASE IF NOT EXISTS xibo_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Créer l'utilisateur (remplacez 'votre_mot_de_passe' par un mot de passe sécurisé)
CREATE USER IF NOT EXISTS 'xibo_user'@'localhost' IDENTIFIED BY 'XiboPass123!';

-- Accorder les privilèges
GRANT ALL PRIVILEGES ON xibo_cms.* TO 'xibo_user'@'localhost';
FLUSH PRIVILEGES;

-- Vérifier la création
SHOW DATABASES LIKE 'xibo_cms';
SELECT User, Host FROM mysql.user WHERE User = 'xibo_user';
"@

$dbScript | Set-Content "$XiboPath\create_database.sql"
Write-Host "Script de base de données créé: $XiboPath\create_database.sql" -ForegroundColor Green

# Créer un script de tâche planifiée
Write-Host "Création du script de maintenance..." -ForegroundColor Yellow
$taskScript = @"
@echo off
REM Script de maintenance Xibo
REM À exécuter via le Planificateur de tâches Windows

cd /d "$XiboPath"
"$XamppPath\php\php.exe" "$XiboPath\bin\xtr.php"
"@

$taskScript | Set-Content "$XiboPath\xibo_maintenance.bat"
Write-Host "Script de maintenance créé: $XiboPath\xibo_maintenance.bat" -ForegroundColor Green

# Instructions finales
Write-Host "`n=== INSTALLATION TERMINÉE ===" -ForegroundColor Green
Write-Host "`nÉtapes suivantes:" -ForegroundColor Yellow
Write-Host "1. Démarrez Apache et MySQL depuis le panneau XAMPP" -ForegroundColor White
Write-Host "2. Allez sur http://localhost/phpmyadmin" -ForegroundColor White
Write-Host "3. Exécutez le script SQL: $XiboPath\create_database.sql" -ForegroundColor White
Write-Host "4. Extrayez manuellement l'archive Xibo dans: $XiboPath" -ForegroundColor White
Write-Host "5. Allez sur http://xibo.local pour commencer l'installation" -ForegroundColor White
Write-Host "6. Configurez la tâche planifiée avec: $XiboPath\xibo_maintenance.bat" -ForegroundColor White

Write-Host "`nInformations de base de données:" -ForegroundColor Cyan
Write-Host "- Host: localhost" -ForegroundColor White
Write-Host "- Database: xibo_cms" -ForegroundColor White
Write-Host "- Username: xibo_user" -ForegroundColor White
Write-Host "- Password: XiboPass123! (à changer)" -ForegroundColor White

Write-Host "`nDossier Library: $LibraryPath" -ForegroundColor Cyan
Write-Host "URL d'accès: http://xibo.local" -ForegroundColor Cyan

Write-Host "`nPour plus d'aide, consultez: install-xibo-xampp.md" -ForegroundColor Yellow
