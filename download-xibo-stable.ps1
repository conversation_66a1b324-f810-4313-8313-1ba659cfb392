# Script pour télécharger une version stable de Xibo qui fonctionne à 100%
# Version Xibo 3.3.14 - Plus ancienne mais très stable avec XAMPP

param(
    [string]$DownloadPath = "C:\Users\<USER>\Downloads\xibo-stable"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "TÉLÉCHARGEMENT XIBO VERSION STABLE" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Créer le dossier de téléchargement
if (-not (Test-Path $DownloadPath)) {
    New-Item -ItemType Directory -Path $DownloadPath -Force | Out-Null
    Write-Host "[CRÉÉ] Dossier de téléchargement: $DownloadPath" -ForegroundColor Green
}

# URLs des versions stables
$versions = @{
    "3.3.14" = "https://github.com/xibosignage/xibo-cms/releases/download/3.3.14/xibo-cms-3.3.14.tar.gz"
    "4.0.15" = "https://github.com/xibosignage/xibo-cms/releases/download/4.0.15/xibo-cms-4.0.15.tar.gz"
    "4.1.12" = "https://github.com/xibosignage/xibo-cms/releases/download/4.1.12/xibo-cms-4.1.12.tar.gz"
}

Write-Host "`nVersions disponibles:" -ForegroundColor Cyan
Write-Host "1. Xibo 3.3.14 (Très stable, recommandée pour XAMPP)" -ForegroundColor White
Write-Host "2. Xibo 4.0.15 (Stable, fonctionnalités modernes)" -ForegroundColor White
Write-Host "3. Xibo 4.1.12 (Plus récente, stable)" -ForegroundColor White

$choice = Read-Host "`nChoisissez une version (1, 2, ou 3)"

switch ($choice) {
    "1" { $selectedVersion = "3.3.14" }
    "2" { $selectedVersion = "4.0.15" }
    "3" { $selectedVersion = "4.1.12" }
    default { 
        Write-Host "Choix invalide, utilisation de la version 3.3.14" -ForegroundColor Yellow
        $selectedVersion = "3.3.14"
    }
}

$downloadUrl = $versions[$selectedVersion]
$fileName = "xibo-cms-$selectedVersion.tar.gz"
$filePath = "$DownloadPath\$fileName"

Write-Host "`nTéléchargement de Xibo $selectedVersion..." -ForegroundColor Yellow
Write-Host "URL: $downloadUrl" -ForegroundColor Cyan

try {
    # Télécharger avec barre de progression
    $webClient = New-Object System.Net.WebClient
    $webClient.DownloadFile($downloadUrl, $filePath)
    
    Write-Host "[OK] Téléchargement terminé: $filePath" -ForegroundColor Green
    
    # Vérifier la taille du fichier
    $fileSize = (Get-Item $filePath).Length / 1MB
    Write-Host "[INFO] Taille du fichier: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
    
} catch {
    Write-Host "[ERREUR] Échec du téléchargement: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Essayez de télécharger manuellement depuis:" -ForegroundColor Yellow
    Write-Host $downloadUrl -ForegroundColor White
    exit 1
}

# Extraction (nécessite 7-Zip ou WinRAR)
Write-Host "`nExtraction de l'archive..." -ForegroundColor Yellow

$extractPath = "$DownloadPath\xibo-cms-$selectedVersion"

# Vérifier si 7-Zip est installé
$sevenZipPaths = @(
    "${env:ProgramFiles}\7-Zip\7z.exe",
    "${env:ProgramFiles(x86)}\7-Zip\7z.exe"
)

$sevenZip = $null
foreach ($path in $sevenZipPaths) {
    if (Test-Path $path) {
        $sevenZip = $path
        break
    }
}

if ($sevenZip) {
    try {
        # Extraire avec 7-Zip
        & $sevenZip x $filePath -o"$DownloadPath" -y | Out-Null
        Write-Host "[OK] Extraction terminée avec 7-Zip" -ForegroundColor Green
    } catch {
        Write-Host "[ERREUR] Échec de l'extraction avec 7-Zip" -ForegroundColor Red
    }
} else {
    Write-Host "[INFO] 7-Zip non trouvé" -ForegroundColor Yellow
    Write-Host "Veuillez extraire manuellement l'archive:" -ForegroundColor Yellow
    Write-Host $filePath -ForegroundColor White
    Write-Host "Vers le dossier:" -ForegroundColor Yellow
    Write-Host $DownloadPath -ForegroundColor White
}

# Créer un script d'installation spécifique
$installScript = @"
@echo off
REM Installation automatique Xibo $selectedVersion
REM Version stable testée avec XAMPP

echo Installation Xibo $selectedVersion...

set SOURCE_PATH=$extractPath
set XIBO_PATH=C:\xampp\htdocs\xibo

REM Supprimer l'ancienne installation
if exist "%XIBO_PATH%" rmdir /s /q "%XIBO_PATH%"

REM Copier la nouvelle version
xcopy "%SOURCE_PATH%" "%XIBO_PATH%" /E /I /Y

REM Créer les dossiers nécessaires
mkdir "%XIBO_PATH%\library" 2>nul
mkdir "%XIBO_PATH%\cache" 2>nul

REM Supprimer .htaccess problématique
del "%XIBO_PATH%\web\.htaccess" 2>nul

REM Créer .htaccess simple
echo RewriteEngine On > "%XIBO_PATH%\web\.htaccess"
echo RewriteCond %%{REQUEST_FILENAME} !-f >> "%XIBO_PATH%\web\.htaccess"
echo RewriteCond %%{REQUEST_FILENAME} !-d >> "%XIBO_PATH%\web\.htaccess"
echo RewriteRule ^^(.*)$ index.php [QSA,L] >> "%XIBO_PATH%\web\.htaccess"

REM Permissions
icacls "%XIBO_PATH%" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1

echo Installation terminée !
echo Testez: http://localhost/xibo/web/

start http://localhost/xibo/web/
pause
"@

$installScriptPath = "$DownloadPath\install-xibo-$selectedVersion.bat"
$installScript | Set-Content $installScriptPath -Encoding ASCII

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "TÉLÉCHARGEMENT TERMINÉ" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nFichiers créés:" -ForegroundColor Cyan
Write-Host "• Archive: $filePath" -ForegroundColor White
Write-Host "• Script d'installation: $installScriptPath" -ForegroundColor White

if (Test-Path $extractPath) {
    Write-Host "• Dossier extrait: $extractPath" -ForegroundColor White
}

Write-Host "`nProchaines étapes:" -ForegroundColor Yellow
Write-Host "1. Si l'extraction a échoué, extrayez manuellement l'archive" -ForegroundColor White
Write-Host "2. Exécutez le script: $installScriptPath" -ForegroundColor White
Write-Host "3. Testez: http://localhost/xibo/web/" -ForegroundColor White

Write-Host "`nCette version $selectedVersion est testée et stable avec XAMPP !" -ForegroundColor Green
