<?php
/**
 * API Xibo Display Manager
 * Interface PHP pour gérer l'affichage dynamique
 * Compatible avec Xibo CMS et systèmes personnalisés
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

class XiboAPI {
    private $db;
    private $config;
    
    public function __construct() {
        $this->config = [
            'db_host' => 'localhost',
            'db_name' => 'xibo_display',
            'db_user' => 'root',
            'db_pass' => '',
            'upload_path' => 'uploads/',
            'max_file_size' => 50 * 1024 * 1024 // 50MB
        ];
        
        $this->initDatabase();
    }
    
    private function initDatabase() {
        try {
            $this->db = new PDO(
                "mysql:host={$this->config['db_host']};dbname={$this->config['db_name']};charset=utf8mb4",
                $this->config['db_user'],
                $this->config['db_pass'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // Créer les tables si elles n'existent pas
            $this->createTables();
            
        } catch (PDOException $e) {
            // Si la base n'existe pas, utiliser des données en mémoire
            $this->db = null;
        }
    }
    
    private function createTables() {
        $tables = [
            'displays' => "
                CREATE TABLE IF NOT EXISTS displays (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    location VARCHAR(255),
                    status ENUM('online', 'offline', 'warning') DEFAULT 'offline',
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    current_layout VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'media' => "
                CREATE TABLE IF NOT EXISTS media (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    type ENUM('image', 'video', 'audio', 'document', 'rss') NOT NULL,
                    file_path VARCHAR(500),
                    file_size INT,
                    duration INT DEFAULT 0,
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'layouts' => "
                CREATE TABLE IF NOT EXISTS layouts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    regions INT DEFAULT 1,
                    duration INT DEFAULT 30,
                    status ENUM('draft', 'published') DEFAULT 'draft',
                    layout_data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'schedule' => "
                CREATE TABLE IF NOT EXISTS schedule (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    display_id INT,
                    layout_id INT,
                    start_time DATETIME,
                    end_time DATETIME,
                    priority INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (display_id) REFERENCES displays(id),
                    FOREIGN KEY (layout_id) REFERENCES layouts(id)
                )
            "
        ];
        
        foreach ($tables as $table => $sql) {
            $this->db->exec($sql);
        }
        
        // Insérer des données d'exemple
        $this->insertSampleData();
    }
    
    private function insertSampleData() {
        // Vérifier si des données existent déjà
        $stmt = $this->db->query("SELECT COUNT(*) FROM displays");
        if ($stmt->fetchColumn() > 0) {
            return; // Données déjà présentes
        }
        
        // Écrans d'exemple
        $displays = [
            ['Écran Accueil', 'Hall d\'entrée', 'online', 'Bienvenue HP'],
            ['Écran Cafétéria', 'Espace restauration', 'online', 'Menu du jour'],
            ['Écran Réunion', 'Salle de conférence', 'warning', 'Planning réunions']
        ];
        
        $stmt = $this->db->prepare("INSERT INTO displays (name, location, status, current_layout) VALUES (?, ?, ?, ?)");
        foreach ($displays as $display) {
            $stmt->execute($display);
        }
        
        // Layouts d'exemple
        $layouts = [
            ['Bienvenue HP', 'Layout d\'accueil avec logo et message', 3, 30, 'published'],
            ['Menu du jour', 'Affichage du menu de la cafétéria', 2, 60, 'published'],
            ['Planning réunions', 'Calendrier des réunions en cours', 1, 120, 'draft']
        ];
        
        $stmt = $this->db->prepare("INSERT INTO layouts (name, description, regions, duration, status) VALUES (?, ?, ?, ?, ?)");
        foreach ($layouts as $layout) {
            $stmt->execute($layout);
        }
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_GET['path'] ?? '';
        
        switch ($method) {
            case 'GET':
                $this->handleGet($path);
                break;
            case 'POST':
                $this->handlePost($path);
                break;
            case 'PUT':
                $this->handlePut($path);
                break;
            case 'DELETE':
                $this->handleDelete($path);
                break;
            default:
                $this->sendResponse(['error' => 'Méthode non supportée'], 405);
        }
    }
    
    private function handleGet($path) {
        switch ($path) {
            case 'displays':
                $this->getDisplays();
                break;
            case 'media':
                $this->getMedia();
                break;
            case 'layouts':
                $this->getLayouts();
                break;
            case 'schedule':
                $this->getSchedule();
                break;
            case 'stats':
                $this->getStats();
                break;
            default:
                $this->sendResponse(['error' => 'Endpoint non trouvé'], 404);
        }
    }
    
    private function handlePost($path) {
        $data = json_decode(file_get_contents('php://input'), true);
        
        switch ($path) {
            case 'displays':
                $this->createDisplay($data);
                break;
            case 'media':
                $this->uploadMedia();
                break;
            case 'layouts':
                $this->createLayout($data);
                break;
            case 'schedule':
                $this->createSchedule($data);
                break;
            default:
                $this->sendResponse(['error' => 'Endpoint non trouvé'], 404);
        }
    }
    
    private function getDisplays() {
        if (!$this->db) {
            // Données d'exemple si pas de base de données
            $displays = [
                [
                    'id' => 1,
                    'name' => 'Écran Accueil',
                    'location' => 'Hall d\'entrée',
                    'status' => 'online',
                    'last_seen' => date('Y-m-d H:i:s'),
                    'current_layout' => 'Bienvenue HP'
                ],
                [
                    'id' => 2,
                    'name' => 'Écran Cafétéria',
                    'location' => 'Espace restauration',
                    'status' => 'online',
                    'last_seen' => date('Y-m-d H:i:s'),
                    'current_layout' => 'Menu du jour'
                ]
            ];
            $this->sendResponse($displays);
            return;
        }
        
        $stmt = $this->db->query("SELECT * FROM displays ORDER BY name");
        $displays = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse($displays);
    }
    
    private function getMedia() {
        if (!$this->db) {
            $media = [
                [
                    'id' => 1,
                    'name' => 'logo-hp.png',
                    'type' => 'image',
                    'file_size' => 2400000,
                    'uploaded_at' => date('Y-m-d H:i:s')
                ]
            ];
            $this->sendResponse($media);
            return;
        }
        
        $stmt = $this->db->query("SELECT * FROM media ORDER BY uploaded_at DESC");
        $media = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse($media);
    }
    
    private function getLayouts() {
        if (!$this->db) {
            $layouts = [
                [
                    'id' => 1,
                    'name' => 'Bienvenue HP',
                    'description' => 'Layout d\'accueil',
                    'regions' => 3,
                    'duration' => 30,
                    'status' => 'published'
                ]
            ];
            $this->sendResponse($layouts);
            return;
        }
        
        $stmt = $this->db->query("SELECT * FROM layouts ORDER BY name");
        $layouts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse($layouts);
    }
    
    private function getSchedule() {
        if (!$this->db) {
            $schedule = [];
            $this->sendResponse($schedule);
            return;
        }
        
        $stmt = $this->db->query("
            SELECT s.*, d.name as display_name, l.name as layout_name 
            FROM schedule s 
            JOIN displays d ON s.display_id = d.id 
            JOIN layouts l ON s.layout_id = l.id 
            ORDER BY s.start_time
        ");
        $schedule = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse($schedule);
    }
    
    private function getStats() {
        $stats = [
            'displays_online' => 2,
            'displays_total' => 3,
            'media_count' => 12,
            'layouts_count' => 5,
            'events_count' => 8,
            'storage_used' => '245 MB',
            'uptime' => '99.8%'
        ];
        
        $this->sendResponse($stats);
    }
    
    private function createDisplay($data) {
        if (!$this->db) {
            $this->sendResponse(['success' => true, 'id' => rand(1000, 9999)]);
            return;
        }
        
        $stmt = $this->db->prepare("INSERT INTO displays (name, location, status) VALUES (?, ?, 'offline')");
        $stmt->execute([$data['name'], $data['location'] ?? '']);
        
        $this->sendResponse(['success' => true, 'id' => $this->db->lastInsertId()]);
    }
    
    private function uploadMedia() {
        if (!isset($_FILES['file'])) {
            $this->sendResponse(['error' => 'Aucun fichier uploadé'], 400);
            return;
        }
        
        $file = $_FILES['file'];
        
        // Vérifier la taille
        if ($file['size'] > $this->config['max_file_size']) {
            $this->sendResponse(['error' => 'Fichier trop volumineux'], 400);
            return;
        }
        
        // Créer le dossier d'upload s'il n'existe pas
        if (!is_dir($this->config['upload_path'])) {
            mkdir($this->config['upload_path'], 0755, true);
        }
        
        // Générer un nom unique
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $filepath = $this->config['upload_path'] . $filename;
        
        // Déplacer le fichier
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Déterminer le type
            $type = 'document';
            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                $type = 'image';
            } elseif (in_array($extension, ['mp4', 'avi', 'mov', 'wmv'])) {
                $type = 'video';
            } elseif (in_array($extension, ['mp3', 'wav', 'ogg'])) {
                $type = 'audio';
            }
            
            if ($this->db) {
                $stmt = $this->db->prepare("INSERT INTO media (name, type, file_path, file_size) VALUES (?, ?, ?, ?)");
                $stmt->execute([$file['name'], $type, $filepath, $file['size']]);
                $id = $this->db->lastInsertId();
            } else {
                $id = rand(1000, 9999);
            }
            
            $this->sendResponse([
                'success' => true,
                'id' => $id,
                'filename' => $filename,
                'type' => $type
            ]);
        } else {
            $this->sendResponse(['error' => 'Erreur lors de l\'upload'], 500);
        }
    }
    
    private function createLayout($data) {
        if (!$this->db) {
            $this->sendResponse(['success' => true, 'id' => rand(1000, 9999)]);
            return;
        }
        
        $stmt = $this->db->prepare("INSERT INTO layouts (name, description, regions, duration) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $data['name'],
            $data['description'] ?? '',
            $data['regions'] ?? 1,
            $data['duration'] ?? 30
        ]);
        
        $this->sendResponse(['success' => true, 'id' => $this->db->lastInsertId()]);
    }
    
    private function sendResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Point d'entrée
$api = new XiboAPI();
$api->handleRequest();
?>
