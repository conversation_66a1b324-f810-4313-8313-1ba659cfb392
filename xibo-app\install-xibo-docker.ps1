# Script d'installation Xibo avec Docker
# Installation automatique et intégration avec votre application

param(
    [string]$InstallPath = "C:\xibo-docker"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "INSTALLATION XIBO AVEC DOCKER" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Vérifier Docker
Write-Host "`nVérification de Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "[OK] Docker détecté: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERREUR] Docker non installé" -ForegroundColor Red
    Write-Host "Téléchargez Docker Desktop: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# Créer le dossier d'installation
Write-Host "`nCréation du dossier d'installation..." -ForegroundColor Yellow
if (-not (Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Write-Host "[OK] Dossier créé: $InstallPath" -ForegroundColor Green
}

Set-Location $InstallPath

# Créer docker-compose.yml
Write-Host "`nCréation de la configuration Docker..." -ForegroundColor Yellow
$dockerCompose = @"
version: '3.1'

services:
  xibo-cms:
    image: xibosignage/xibo-cms:release-4.0.15
    volumes:
      - ./shared/cms/custom:/var/www/cms/custom
      - ./shared/backup:/var/www/backup
      - ./shared/cms/web/userscripts:/var/www/cms/web/userscripts
      - ./shared/cms/library:/var/www/cms/library
      - ./shared/cms/cache:/var/www/cms/cache
      - ./shared/cms/web/cache:/var/www/cms/web/cache
    environment:
      - MYSQL_HOST=xibo-db
      - MYSQL_USER=xibo
      - MYSQL_PASSWORD=xibo123
      - MYSQL_DATABASE=xibo
      - CMS_SERVER_NAME=localhost
      - XMR_HOST=xibo-xmr
      - CMS_SECRET_KEY=xiboHP2025SecretKey
    ports:
      - "8080:80"
    depends_on:
      - xibo-db
      - xibo-xmr
    restart: unless-stopped

  xibo-xmr:
    image: xibosignage/xibo-xmr:release-0.10
    ports:
      - "9505:9505"
    volumes:
      - ./shared/cms/custom:/var/www/cms/custom
    restart: unless-stopped

  xibo-db:
    image: mysql:8.0
    volumes:
      - ./shared/db:/var/lib/mysql
    restart: unless-stopped
    environment:
      - MYSQL_DATABASE=xibo
      - MYSQL_USER=xibo
      - MYSQL_PASSWORD=xibo123
      - MYSQL_ROOT_PASSWORD=root123
    command: --default-authentication-plugin=mysql_native_password --sql-mode=""
    ports:
      - "3307:3306"

volumes:
  shared:
"@

$dockerCompose | Out-File -FilePath "docker-compose.yml" -Encoding UTF8
Write-Host "[OK] Configuration Docker créée" -ForegroundColor Green

# Créer le fichier de configuration
$configEnv = @"
# Configuration Xibo Docker
MYSQL_HOST=xibo-db
MYSQL_USER=xibo
MYSQL_PASSWORD=xibo123
MYSQL_DATABASE=xibo
MYSQL_ROOT_PASSWORD=root123

CMS_SERVER_NAME=localhost
CMS_ALIAS=localhost:8080
CMS_SECRET_KEY=xiboHP2025SecretKey

XMR_HOST=xibo-xmr
"@

$configEnv | Out-File -FilePath "config.env" -Encoding UTF8

# Créer les dossiers nécessaires
Write-Host "`nCréation des dossiers..." -ForegroundColor Yellow
$folders = @(
    "shared/cms/custom",
    "shared/cms/library", 
    "shared/cms/cache",
    "shared/cms/web/cache",
    "shared/cms/web/userscripts",
    "shared/backup",
    "shared/db"
)

foreach ($folder in $folders) {
    New-Item -ItemType Directory -Path $folder -Force | Out-Null
}
Write-Host "[OK] Dossiers créés" -ForegroundColor Green

# Télécharger les images Docker
Write-Host "`nTéléchargement des images Docker..." -ForegroundColor Yellow
Write-Host "Cela peut prendre plusieurs minutes..." -ForegroundColor Cyan

try {
    docker-compose pull
    Write-Host "[OK] Images téléchargées" -ForegroundColor Green
} catch {
    Write-Host "[ERREUR] Échec du téléchargement" -ForegroundColor Red
    Write-Host "Vérifiez votre connexion Internet" -ForegroundColor Yellow
    exit 1
}

# Démarrer Xibo
Write-Host "`nDémarrage de Xibo..." -ForegroundColor Yellow
try {
    docker-compose up -d
    Write-Host "[OK] Xibo démarré" -ForegroundColor Green
} catch {
    Write-Host "[ERREUR] Échec du démarrage" -ForegroundColor Red
    exit 1
}

# Attendre que les services soient prêts
Write-Host "`nAttente du démarrage des services..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Vérifier l'état des conteneurs
Write-Host "`nVérification des services..." -ForegroundColor Yellow
$containers = docker-compose ps --services
foreach ($container in $containers) {
    $status = docker-compose ps $container
    if ($status -match "Up") {
        Write-Host "[OK] $container démarré" -ForegroundColor Green
    } else {
        Write-Host "[ATTENTION] $container problème" -ForegroundColor Yellow
    }
}

# Créer un script de gestion
$manageScript = @"
@echo off
REM Script de gestion Xibo Docker

echo ========================================
echo GESTION XIBO DOCKER
echo ========================================
echo.
echo 1. Démarrer Xibo
echo 2. Arrêter Xibo  
echo 3. Redémarrer Xibo
echo 4. Voir les logs
echo 5. Mise à jour
echo 6. Sauvegarde
echo 7. Quitter
echo.

set /p choice="Votre choix (1-7): "

if "%choice%"=="1" (
    echo Démarrage de Xibo...
    docker-compose up -d
    echo Xibo démarré sur http://localhost:8080
)

if "%choice%"=="2" (
    echo Arrêt de Xibo...
    docker-compose down
    echo Xibo arrêté
)

if "%choice%"=="3" (
    echo Redémarrage de Xibo...
    docker-compose restart
    echo Xibo redémarré
)

if "%choice%"=="4" (
    echo Logs Xibo:
    docker-compose logs -f --tail=50
)

if "%choice%"=="5" (
    echo Mise à jour de Xibo...
    docker-compose pull
    docker-compose up -d
    echo Mise à jour terminée
)

if "%choice%"=="6" (
    echo Sauvegarde en cours...
    docker exec xibo-docker_xibo-db_1 mysqldump -u xibo -pxibo123 xibo > backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.sql
    echo Sauvegarde terminée
)

if "%choice%"=="7" exit

pause
goto :eof
"@

$manageScript | Out-File -FilePath "manage-xibo.bat" -Encoding ASCII
Write-Host "[OK] Script de gestion créé: manage-xibo.bat" -ForegroundColor Green

# Créer un raccourci bureau
$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutPath = "$desktopPath\Xibo Docker.lnk"

try {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = "http://localhost:8080"
    $Shortcut.Description = "Accéder à Xibo CMS"
    $Shortcut.Save()
    Write-Host "[OK] Raccourci bureau créé" -ForegroundColor Green
} catch {
    Write-Host "[INFO] Impossible de créer le raccourci" -ForegroundColor Cyan
}

# Mettre à jour la configuration de l'application
$appConfigPath = "C:\xampp\htdocs\xibo-app\xibo-config.js"
$appConfig = @"
// Configuration automatique pour Xibo Docker
window.xiboConfig = {
    cms_url: 'http://localhost:8080',
    api_url: 'http://localhost:8080/api',
    docker_mode: true,
    installation_path: '$InstallPath',
    database: {
        host: 'localhost',
        port: 3307,
        database: 'xibo',
        username: 'xibo',
        password: 'xibo123'
    }
};

console.log('Configuration Xibo Docker chargée');
"@

if (Test-Path "C:\xampp\htdocs\xibo-app") {
    $appConfig | Out-File -FilePath $appConfigPath -Encoding UTF8
    Write-Host "[OK] Configuration de l'application mise à jour" -ForegroundColor Green
}

# Résumé final
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "INSTALLATION TERMINÉE AVEC SUCCÈS !" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nInformations d'accès:" -ForegroundColor Cyan
Write-Host "• URL Xibo CMS: http://localhost:8080" -ForegroundColor White
Write-Host "• Votre application: http://localhost/xibo-app" -ForegroundColor White
Write-Host "• Base de données: localhost:3307" -ForegroundColor White

Write-Host "`nComptes par défaut:" -ForegroundColor Cyan
Write-Host "• Base de données: xibo / xibo123" -ForegroundColor White
Write-Host "• Admin Xibo: À créer lors de la première connexion" -ForegroundColor White

Write-Host "`nFichiers utiles:" -ForegroundColor Cyan
Write-Host "• Gestion: $InstallPath\manage-xibo.bat" -ForegroundColor White
Write-Host "• Configuration: $InstallPath\config.env" -ForegroundColor White
Write-Host "• Docker Compose: $InstallPath\docker-compose.yml" -ForegroundColor White

Write-Host "`nCommandes Docker utiles:" -ForegroundColor Cyan
Write-Host "• Démarrer: docker-compose up -d" -ForegroundColor White
Write-Host "• Arrêter: docker-compose down" -ForegroundColor White
Write-Host "• Logs: docker-compose logs -f" -ForegroundColor White

Write-Host "`nProchaines étapes:" -ForegroundColor Yellow
Write-Host "1. Accédez à http://localhost:8080" -ForegroundColor White
Write-Host "2. Suivez l'assistant d'installation Xibo" -ForegroundColor White
Write-Host "3. Utilisez les paramètres de base de données fournis" -ForegroundColor White
Write-Host "4. Votre application se connectera automatiquement" -ForegroundColor White

Write-Host "`n🎉 Xibo Docker est maintenant opérationnel !" -ForegroundColor Green

# Ouvrir automatiquement le navigateur
Start-Process "http://localhost:8080"
