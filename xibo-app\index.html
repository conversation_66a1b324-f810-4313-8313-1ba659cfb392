<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xibo Display Manager - Application HP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .display-preview {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            min-height: 200px;
            overflow-y: auto;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        
        .btn-custom {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: scale(1.05);
            color: white;
        }
        
        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .media-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }
        
        .media-item:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-tv"></i> Xibo Display Manager</h1>
                <p class="mb-0">Système d'affichage dynamique - Installation HP</p>
                <div class="mt-3">
                    <span class="status-indicator status-online"></span>
                    <span id="connection-status">Système opérationnel</span>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt"></i> Tableau de bord
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#displays" onclick="showSection('displays')">
                                <i class="fas fa-desktop"></i> Écrans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#media" onclick="showSection('media')">
                                <i class="fas fa-images"></i> Médias
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#layouts" onclick="showSection('layouts')">
                                <i class="fas fa-th-large"></i> Layouts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#schedule" onclick="showSection('schedule')">
                                <i class="fas fa-calendar"></i> Planification
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="row p-4">
                    <div class="col-md-3">
                        <div class="feature-card text-center">
                            <i class="fas fa-desktop fa-3x text-primary mb-3"></i>
                            <h5>Écrans actifs</h5>
                            <h2 class="text-primary" id="active-displays">3</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card text-center">
                            <i class="fas fa-images fa-3x text-success mb-3"></i>
                            <h5>Médias</h5>
                            <h2 class="text-success" id="media-count">12</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card text-center">
                            <i class="fas fa-th-large fa-3x text-warning mb-3"></i>
                            <h5>Layouts</h5>
                            <h2 class="text-warning" id="layout-count">5</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card text-center">
                            <i class="fas fa-calendar fa-3x text-info mb-3"></i>
                            <h5>Événements</h5>
                            <h2 class="text-info" id="event-count">8</h2>
                        </div>
                    </div>
                </div>

                <div class="row p-4">
                    <div class="col-md-8">
                        <div class="feature-card">
                            <h5><i class="fas fa-chart-line"></i> Activité en temps réel</h5>
                            <div class="display-preview" id="activity-log">
                                <div>[2025-01-07 16:30:15] Écran-1: Affichage du layout "Bienvenue"</div>
                                <div>[2025-01-07 16:30:10] Écran-2: Mise à jour du contenu</div>
                                <div>[2025-01-07 16:30:05] Système: Synchronisation réussie</div>
                                <div>[2025-01-07 16:30:00] Écran-3: Connexion établie</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <h5><i class="fas fa-cog"></i> Actions rapides</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-custom" onclick="createLayout()">
                                    <i class="fas fa-plus"></i> Nouveau Layout
                                </button>
                                <button class="btn btn-custom" onclick="uploadMedia()">
                                    <i class="fas fa-upload"></i> Uploader Média
                                </button>
                                <button class="btn btn-custom" onclick="scheduleContent()">
                                    <i class="fas fa-clock"></i> Planifier
                                </button>
                                <button class="btn btn-custom" onclick="xiboIntegration && xiboIntegration.openXiboCMS()">
                                    <i class="fas fa-external-link-alt"></i> Ouvrir Xibo CMS
                                </button>
                                <button class="btn btn-custom" onclick="viewReports()">
                                    <i class="fas fa-chart-bar"></i> Rapports
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Displays Section -->
            <div id="displays" class="section" style="display: none;">
                <div class="p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="fas fa-desktop"></i> Gestion des écrans</h3>
                        <button class="btn btn-custom" onclick="addDisplay()">
                            <i class="fas fa-plus"></i> Ajouter un écran
                        </button>
                    </div>
                    
                    <div class="row" id="displays-container">
                        <!-- Les écrans seront ajoutés dynamiquement -->
                    </div>
                </div>
            </div>

            <!-- Media Section -->
            <div id="media" class="section" style="display: none;">
                <div class="p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="fas fa-images"></i> Bibliothèque de médias</h3>
                        <button class="btn btn-custom" onclick="uploadMedia()">
                            <i class="fas fa-upload"></i> Uploader
                        </button>
                    </div>
                    
                    <div class="media-grid" id="media-container">
                        <!-- Les médias seront ajoutés dynamiquement -->
                    </div>
                </div>
            </div>

            <!-- Layouts Section -->
            <div id="layouts" class="section" style="display: none;">
                <div class="p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="fas fa-th-large"></i> Layouts d'affichage</h3>
                        <button class="btn btn-custom" onclick="createLayout()">
                            <i class="fas fa-plus"></i> Créer Layout
                        </button>
                    </div>
                    
                    <div class="row" id="layouts-container">
                        <!-- Les layouts seront ajoutés dynamiquement -->
                    </div>
                </div>
            </div>

            <!-- Schedule Section -->
            <div id="schedule" class="section" style="display: none;">
                <div class="p-4">
                    <h3><i class="fas fa-calendar"></i> Planification des contenus</h3>
                    <div class="feature-card">
                        <div id="calendar-container">
                            <!-- Calendrier sera ajouté ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
    <script src="xibo-integration.js"></script>
</body>
</html>
