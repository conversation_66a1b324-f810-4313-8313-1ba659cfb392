@echo off
REM Script pour déplacer et configurer Xibo CMS existant
REM À exécuter en tant qu'administrateur

echo ========================================
echo Configuration Xibo CMS existant
echo ========================================

REM Définir les chemins
set SOURCE_PATH=C:\Users\<USER>\Downloads\Prodynamique\xibo-cms-4.2.3
set XAMPP_PATH=C:\xampp
set XIBO_PATH=%XAMPP_PATH%\htdocs\xibo
set LIBRARY_PATH=%XIBO_PATH%\library
set CACHE_PATH=%XIBO_PATH%\cache

echo Chemins configurés:
echo - Source Xibo: %SOURCE_PATH%
echo - XAMPP: %XAMPP_PATH%
echo - Destination: %XIBO_PATH%
echo.

REM Vérifier si XAMPP existe
if not exist "%XAMPP_PATH%" (
    echo ERREUR: XAMPP non trouvé dans %XAMPP_PATH%
    echo Veuillez vérifier l'installation XAMPP
    pause
    exit /b 1
)

REM Vérifier si Xibo source existe
if not exist "%SOURCE_PATH%" (
    echo ERREUR: Xibo CMS non trouvé dans %SOURCE_PATH%
    echo Veuillez vérifier le chemin du téléchargement
    pause
    exit /b 1
)

echo [OK] XAMPP trouvé
echo [OK] Xibo CMS source trouvé
echo.

REM Créer le dossier de destination si nécessaire
if not exist "%XAMPP_PATH%\htdocs" mkdir "%XAMPP_PATH%\htdocs"

REM Supprimer le dossier xibo existant s'il existe
if exist "%XIBO_PATH%" (
    echo Suppression de l'installation Xibo existante...
    rmdir /s /q "%XIBO_PATH%"
)

echo Déplacement des fichiers Xibo...
echo Cela peut prendre quelques minutes...

REM Copier tout le contenu de Xibo vers la destination
xcopy "%SOURCE_PATH%" "%XIBO_PATH%" /E /I /H /Y

if %ERRORLEVEL% NEQ 0 (
    echo ERREUR lors de la copie des fichiers
    pause
    exit /b 1
)

echo [OK] Fichiers Xibo copiés avec succès
echo.

REM Créer les dossiers nécessaires s'ils n'existent pas
echo Création des dossiers nécessaires...
if not exist "%LIBRARY_PATH%" mkdir "%LIBRARY_PATH%"
if not exist "%CACHE_PATH%" mkdir "%CACHE_PATH%"
if not exist "%LIBRARY_PATH%\temp" mkdir "%LIBRARY_PATH%\temp"
if not exist "%LIBRARY_PATH%\screenshots" mkdir "%LIBRARY_PATH%\screenshots"
if not exist "%LIBRARY_PATH%\playersoftware" mkdir "%LIBRARY_PATH%\playersoftware"
if not exist "%LIBRARY_PATH%\certs" mkdir "%LIBRARY_PATH%\certs"

echo [OK] Dossiers créés
echo.

REM Configurer les permissions
echo Configuration des permissions...
icacls "%XIBO_PATH%" /grant "%USERNAME%:(OI)(CI)F" /T >nul 2>&1
icacls "%XIBO_PATH%" /grant "IUSR:(OI)(CI)F" /T >nul 2>&1
icacls "%XIBO_PATH%" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
icacls "%LIBRARY_PATH%" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
icacls "%CACHE_PATH%" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1

echo [OK] Permissions configurées
echo.

REM Créer un fichier .htaccess pour protéger le dossier library
echo Création des fichiers de sécurité...
echo # Protection du dossier library > "%LIBRARY_PATH%\.htaccess"
echo Order Deny,Allow >> "%LIBRARY_PATH%\.htaccess"
echo Deny from all >> "%LIBRARY_PATH%\.htaccess"
echo # Autoriser seulement les fichiers médias >> "%LIBRARY_PATH%\.htaccess"
echo ^<Files ~ "\.(jpg|jpeg|png|gif|mp4|avi|pdf|ppt|pptx|mov|wmv|flv|webm)$"^> >> "%LIBRARY_PATH%\.htaccess"
echo Allow from all >> "%LIBRARY_PATH%\.htaccess"
echo ^</Files^> >> "%LIBRARY_PATH%\.htaccess"

REM Créer un fichier index.php de protection
echo ^<?php > "%LIBRARY_PATH%\index.php"
echo // Protection du dossier library >> "%LIBRARY_PATH%\index.php"
echo header('HTTP/1.0 403 Forbidden'); >> "%LIBRARY_PATH%\index.php"
echo exit('Accès interdit'); >> "%LIBRARY_PATH%\index.php"
echo ?^> >> "%LIBRARY_PATH%\index.php"

echo [OK] Fichiers de sécurité créés
echo.

REM Créer un fichier de test des permissions
echo Création du fichier de test...
echo ^<?php > "%XIBO_PATH%\test-permissions.php"
echo echo "^<h2^>Test des permissions Xibo^</h2^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "^<p^>Version PHP: " . phpversion() . "^</p^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "^<p^>Dossier library: " . (is_writable('%LIBRARY_PATH%') ? '^<span style=\"color:green\"^>OK^</span^>' : '^<span style=\"color:red\"^>ERREUR^</span^>') . "^</p^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "^<p^>Dossier cache: " . (is_writable('%CACHE_PATH%') ? '^<span style=\"color:green\"^>OK^</span^>' : '^<span style=\"color:red\"^>ERREUR^</span^>') . "^</p^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "^<p^>Dossier certs: " . (is_writable('%LIBRARY_PATH%\certs') ? '^<span style=\"color:green\"^>OK^</span^>' : '^<span style=\"color:red\"^>ERREUR^</span^>') . "^</p^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "^<hr^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "^<p^>Extensions PHP requises:^</p^>"; >> "%XIBO_PATH%\test-permissions.php"
echo $extensions = ['gd', 'pdo_mysql', 'zip', 'curl', 'soap', 'mbstring', 'fileinfo', 'dom', 'xml']; >> "%XIBO_PATH%\test-permissions.php"
echo foreach($extensions as $ext) { >> "%XIBO_PATH%\test-permissions.php"
echo     echo "^<p^>" . $ext . ": " . (extension_loaded($ext) ? '^<span style=\"color:green\"^>OK^</span^>' : '^<span style=\"color:red\"^>MANQUANT^</span^>') . "^</p^>"; >> "%XIBO_PATH%\test-permissions.php"
echo } >> "%XIBO_PATH%\test-permissions.php"
echo ?^> >> "%XIBO_PATH%\test-permissions.php"

echo [OK] Fichier de test créé
echo.

REM Vérifier la structure des fichiers
echo Vérification de la structure...
if exist "%XIBO_PATH%\web\index.php" (
    echo [OK] Fichier web/index.php trouvé
) else (
    echo [ATTENTION] Fichier web/index.php manquant
)

if exist "%XIBO_PATH%\bin\xtr.php" (
    echo [OK] Fichier bin/xtr.php trouvé
) else (
    echo [ATTENTION] Fichier bin/xtr.php manquant
)

if exist "%XIBO_PATH%\web\.htaccess" (
    echo [OK] Fichier web/.htaccess trouvé
) else (
    echo [ATTENTION] Fichier web/.htaccess manquant
)

echo.

REM Afficher les informations de configuration
echo ========================================
echo CONFIGURATION TERMINÉE
echo ========================================
echo.
echo Structure Xibo installée dans: %XIBO_PATH%
echo Dossier library: %LIBRARY_PATH%
echo Dossier cache: %CACHE_PATH%
echo.
echo Prochaines étapes:
echo 1. Démarrer Apache et MySQL dans XAMPP
echo 2. Vérifier la configuration PHP
echo 3. Créer la base de données MySQL
echo 4. Configurer le Virtual Host (optionnel)
echo 5. Accéder à l'installation web
echo.
echo URLs de test:
echo - Test permissions: http://localhost/xibo/test-permissions.php
echo - Installation Xibo: http://localhost/xibo
echo.
echo Fichiers créés:
echo - %XIBO_PATH%\test-permissions.php
echo - %LIBRARY_PATH%\.htaccess
echo - %LIBRARY_PATH%\index.php
echo.

pause
