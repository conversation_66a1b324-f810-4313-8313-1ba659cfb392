@echo off
REM Script pour configurer XAMPP pour Xibo CMS
REM À exécuter en tant qu'administrateur

echo ========================================
echo Configuration XAMPP pour Xibo CMS
echo ========================================

REM Définir les chemins
set XAMPP_PATH=C:\xampp
set PHP_INI=%XAMPP_PATH%\php\php.ini
set APACHE_CONF=%XAMPP_PATH%\apache\conf\httpd.conf
set VHOST_CONF=%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf
set HOSTS_FILE=%SystemRoot%\System32\drivers\etc\hosts

echo Vérification de l'installation XAMPP...

REM Vérifier XAMPP
if not exist "%XAMPP_PATH%" (
    echo ERREUR: XAMPP non trouvé dans %XAMPP_PATH%
    echo Veuillez installer XAMPP depuis https://www.apachefriends.org/
    pause
    exit /b 1
)

if not exist "%PHP_INI%" (
    echo ERREUR: Fichier php.ini non trouvé
    pause
    exit /b 1
)

echo [OK] XAMPP trouvé
echo.

REM Vérifier la version PHP
echo Vérification de la version PHP...
"%XAMPP_PATH%\php\php.exe" --version | findstr "PHP 8" >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] PHP 8.x détecté
) else (
    echo [ATTENTION] PHP 8.x recommandé pour Xibo 4.2.3
    "%XAMPP_PATH%\php\php.exe" --version
)
echo.

REM Sauvegarder php.ini original
if not exist "%PHP_INI%.backup" (
    echo Sauvegarde du fichier php.ini original...
    copy "%PHP_INI%" "%PHP_INI%.backup" >nul
    echo [OK] Sauvegarde créée: php.ini.backup
)

REM Configurer PHP.ini pour Xibo
echo Configuration de PHP pour Xibo...

REM Créer un fichier temporaire avec les modifications
echo ; Configuration PHP pour Xibo CMS > "%TEMP%\xibo_php_config.txt"
echo memory_limit = 256M >> "%TEMP%\xibo_php_config.txt"
echo upload_max_filesize = 128M >> "%TEMP%\xibo_php_config.txt"
echo post_max_size = 128M >> "%TEMP%\xibo_php_config.txt"
echo max_execution_time = 300 >> "%TEMP%\xibo_php_config.txt"
echo max_input_time = 300 >> "%TEMP%\xibo_php_config.txt"
echo max_input_vars = 3000 >> "%TEMP%\xibo_php_config.txt"
echo session.gc_maxlifetime = 7200 >> "%TEMP%\xibo_php_config.txt"
echo date.timezone = Europe/Paris >> "%TEMP%\xibo_php_config.txt"

REM Vérifier et activer les extensions PHP requises
echo.
echo Vérification des extensions PHP...

set EXTENSIONS=gd pdo_mysql zip curl soap mbstring fileinfo dom xml simplexml json ctype iconv

for %%e in (%EXTENSIONS%) do (
    findstr /C:"extension=%%e" "%PHP_INI%" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Extension %%e trouvée
    ) else (
        findstr /C:";extension=%%e" "%PHP_INI%" >nul
        if %ERRORLEVEL% EQU 0 (
            echo [MODIFICATION] Activation de l'extension %%e
            powershell -Command "(Get-Content '%PHP_INI%') -replace ';extension=%%e', 'extension=%%e' | Set-Content '%PHP_INI%'"
        ) else (
            echo [ATTENTION] Extension %%e non trouvée dans php.ini
        )
    )
)

REM Modifier les paramètres PHP
echo.
echo Modification des paramètres PHP...

REM Memory limit
findstr /C:"memory_limit" "%PHP_INI%" | findstr /C:"256M" >nul
if %ERRORLEVEL% NEQ 0 (
    powershell -Command "(Get-Content '%PHP_INI%') -replace 'memory_limit = .*', 'memory_limit = 256M' | Set-Content '%PHP_INI%'"
    echo [MODIFIÉ] memory_limit = 256M
)

REM Upload max filesize
findstr /C:"upload_max_filesize" "%PHP_INI%" | findstr /C:"128M" >nul
if %ERRORLEVEL% NEQ 0 (
    powershell -Command "(Get-Content '%PHP_INI%') -replace 'upload_max_filesize = .*', 'upload_max_filesize = 128M' | Set-Content '%PHP_INI%'"
    echo [MODIFIÉ] upload_max_filesize = 128M
)

REM Post max size
findstr /C:"post_max_size" "%PHP_INI%" | findstr /C:"128M" >nul
if %ERRORLEVEL% NEQ 0 (
    powershell -Command "(Get-Content '%PHP_INI%') -replace 'post_max_size = .*', 'post_max_size = 128M' | Set-Content '%PHP_INI%'"
    echo [MODIFIÉ] post_max_size = 128M
)

REM Max execution time
findstr /C:"max_execution_time" "%PHP_INI%" | findstr /C:"300" >nul
if %ERRORLEVEL% NEQ 0 (
    powershell -Command "(Get-Content '%PHP_INI%') -replace 'max_execution_time = .*', 'max_execution_time = 300' | Set-Content '%PHP_INI%'"
    echo [MODIFIÉ] max_execution_time = 300
)

echo [OK] Configuration PHP terminée
echo.

REM Vérifier mod_rewrite Apache
echo Vérification de la configuration Apache...
findstr /C:"LoadModule rewrite_module" "%APACHE_CONF%" | findstr /V /C:"#" >nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] mod_rewrite activé
) else (
    echo [MODIFICATION] Activation de mod_rewrite
    powershell -Command "(Get-Content '%APACHE_CONF%') -replace '#LoadModule rewrite_module', 'LoadModule rewrite_module' | Set-Content '%APACHE_CONF%'"
)

REM Configurer Virtual Host pour Xibo (optionnel)
echo.
echo Configuration du Virtual Host...
findstr /C:"xibo.local" "%VHOST_CONF%" >nul
if %ERRORLEVEL% NEQ 0 (
    echo.>> "%VHOST_CONF%"
    echo # Xibo Virtual Host >> "%VHOST_CONF%"
    echo ^<VirtualHost *:80^> >> "%VHOST_CONF%"
    echo     DocumentRoot "C:/xampp/htdocs/xibo/web" >> "%VHOST_CONF%"
    echo     ServerName xibo.local >> "%VHOST_CONF%"
    echo     ServerAlias www.xibo.local >> "%VHOST_CONF%"
    echo. >> "%VHOST_CONF%"
    echo     ^<Directory "C:/xampp/htdocs/xibo/web"^> >> "%VHOST_CONF%"
    echo         AllowOverride All >> "%VHOST_CONF%"
    echo         Options Indexes FollowSymLinks MultiViews >> "%VHOST_CONF%"
    echo         Require all granted >> "%VHOST_CONF%"
    echo         DirectoryIndex index.php >> "%VHOST_CONF%"
    echo     ^</Directory^> >> "%VHOST_CONF%"
    echo. >> "%VHOST_CONF%"
    echo     ErrorLog "logs/xibo_error.log" >> "%VHOST_CONF%"
    echo     CustomLog "logs/xibo_access.log" combined >> "%VHOST_CONF%"
    echo ^</VirtualHost^> >> "%VHOST_CONF%"
    
    echo [OK] Virtual Host ajouté
) else (
    echo [OK] Virtual Host déjà configuré
)

REM Modifier le fichier hosts
echo.
echo Configuration du fichier hosts...
findstr /C:"xibo.local" "%HOSTS_FILE%" >nul
if %ERRORLEVEL% NEQ 0 (
    echo 127.0.0.1 xibo.local >> "%HOSTS_FILE%"
    echo 127.0.0.1 www.xibo.local >> "%HOSTS_FILE%"
    echo [OK] Entrées hosts ajoutées
) else (
    echo [OK] Entrées hosts déjà présentes
)

REM Créer un script de démarrage XAMPP
echo.
echo Création du script de démarrage...
echo @echo off > "%XAMPP_PATH%\start-xibo.bat"
echo echo Démarrage des services XAMPP pour Xibo... >> "%XAMPP_PATH%\start-xibo.bat"
echo cd /d "%XAMPP_PATH%" >> "%XAMPP_PATH%\start-xibo.bat"
echo start xampp_start.exe >> "%XAMPP_PATH%\start-xibo.bat"
echo timeout /t 5 >> "%XAMPP_PATH%\start-xibo.bat"
echo echo Services démarrés. >> "%XAMPP_PATH%\start-xibo.bat"
echo echo Accédez à: http://xibo.local >> "%XAMPP_PATH%\start-xibo.bat"
echo pause >> "%XAMPP_PATH%\start-xibo.bat"

echo [OK] Script de démarrage créé: %XAMPP_PATH%\start-xibo.bat
echo.

REM Nettoyer les fichiers temporaires
del "%TEMP%\xibo_php_config.txt" 2>nul

echo ========================================
echo CONFIGURATION XAMPP TERMINÉE
echo ========================================
echo.
echo Modifications apportées:
echo - Extensions PHP activées pour Xibo
echo - Paramètres PHP optimisés (memory, upload, execution time)
echo - mod_rewrite Apache activé
echo - Virtual Host configuré (xibo.local)
echo - Fichier hosts modifié
echo.
echo IMPORTANT: Redémarrez Apache et MySQL pour appliquer les changements
echo.
echo Prochaines étapes:
echo 1. Redémarrer XAMPP (Apache + MySQL)
echo 2. Tester: http://localhost/xibo/test-permissions.php
echo 3. Créer la base de données MySQL
echo 4. Lancer l'installation web Xibo
echo.
echo Scripts utiles:
echo - Démarrage: %XAMPP_PATH%\start-xibo.bat
echo - Panneau XAMPP: %XAMPP_PATH%\xampp-control.exe
echo.

pause
