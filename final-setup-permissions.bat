@echo off
REM Script final pour configurer les permissions et finaliser l'installation Xibo
REM À exécuter en tant qu'administrateur après avoir déplacé les fichiers

echo ========================================
echo Configuration finale Xibo CMS
echo ========================================

REM Définir les chemins
set XAMPP_PATH=C:\xampp
set XIBO_PATH=%XAMPP_PATH%\htdocs\xibo
set LIBRARY_PATH=%XIBO_PATH%\library
set CACHE_PATH=%XIBO_PATH%\cache
set WEB_PATH=%XIBO_PATH%\web

echo Vérification de l'installation...

REM Vérifier que Xibo est bien installé
if not exist "%XIBO_PATH%\web\index.php" (
    echo ERREUR: Xibo CMS non trouvé dans %XIBO_PATH%
    echo Veuillez d'abord exécuter move-configure-xibo.bat
    pause
    exit /b 1
)

echo [OK] Xibo CMS trouvé
echo.

REM Vérifier et créer tous les dossiers nécessaires
echo Vérification des dossiers...
set FOLDERS=%LIBRARY_PATH% %CACHE_PATH% %LIBRARY_PATH%\temp %LIBRARY_PATH%\screenshots %LIBRARY_PATH%\playersoftware %LIBRARY_PATH%\certs %LIBRARY_PATH%\log

for %%f in (%FOLDERS%) do (
    if not exist "%%f" (
        mkdir "%%f"
        echo [CRÉÉ] %%f
    ) else (
        echo [OK] %%f
    )
)

echo.

REM Configuration avancée des permissions
echo Configuration des permissions avancées...

REM Permissions pour l'utilisateur HP
icacls "%XIBO_PATH%" /grant "HP:(OI)(CI)F" /T >nul 2>&1
echo [OK] Permissions utilisateur HP

REM Permissions pour Apache/IIS
icacls "%XIBO_PATH%" /grant "IUSR:(OI)(CI)F" /T >nul 2>&1
icacls "%XIBO_PATH%" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
echo [OK] Permissions serveur web

REM Permissions spéciales pour les dossiers critiques
icacls "%LIBRARY_PATH%" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
icacls "%CACHE_PATH%" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
echo [OK] Permissions dossiers critiques

REM Permissions pour les logs
icacls "%LIBRARY_PATH%\log" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
echo [OK] Permissions logs

echo.

REM Créer les fichiers de sécurité et configuration
echo Création des fichiers de sécurité...

REM .htaccess pour library
if not exist "%LIBRARY_PATH%\.htaccess" (
    echo # Protection du dossier library Xibo > "%LIBRARY_PATH%\.htaccess"
    echo Order Deny,Allow >> "%LIBRARY_PATH%\.htaccess"
    echo Deny from all >> "%LIBRARY_PATH%\.htaccess"
    echo # Autoriser les fichiers médias >> "%LIBRARY_PATH%\.htaccess"
    echo ^<Files ~ "\.(jpg|jpeg|png|gif|bmp|webp|mp4|avi|mov|wmv|flv|webm|mp3|wav|pdf|ppt|pptx|doc|docx)$"^> >> "%LIBRARY_PATH%\.htaccess"
    echo Allow from all >> "%LIBRARY_PATH%\.htaccess"
    echo ^</Files^> >> "%LIBRARY_PATH%\.htaccess"
    echo [CRÉÉ] .htaccess library
)

REM index.php de protection pour library
if not exist "%LIBRARY_PATH%\index.php" (
    echo ^<?php > "%LIBRARY_PATH%\index.php"
    echo // Protection du dossier library >> "%LIBRARY_PATH%\index.php"
    echo header('HTTP/1.0 403 Forbidden'); >> "%LIBRARY_PATH%\index.php"
    echo exit('Accès interdit au dossier library'); >> "%LIBRARY_PATH%\index.php"
    echo ?^> >> "%LIBRARY_PATH%\index.php"
    echo [CRÉÉ] index.php library
)

REM .htaccess pour cache
if not exist "%CACHE_PATH%\.htaccess" (
    echo # Protection du dossier cache > "%CACHE_PATH%\.htaccess"
    echo Order Deny,Allow >> "%CACHE_PATH%\.htaccess"
    echo Deny from all >> "%CACHE_PATH%\.htaccess"
    echo [CRÉÉ] .htaccess cache
)

echo.

REM Créer un fichier de test complet
echo Création du fichier de test avancé...
echo ^<?php > "%XIBO_PATH%\test-installation.php"
echo echo "^<html^>^<head^>^<title^>Test Installation Xibo^</title^>^</head^>^<body^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<h1^>Test d'installation Xibo CMS 4.2.3^</h1^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<h2^>Informations système^</h2^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>Version PHP: " . phpversion() . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>Système: " . php_uname() . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>Date/Heure: " . date('Y-m-d H:i:s') . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<h2^>Test des dossiers^</h2^>"; >> "%XIBO_PATH%\test-installation.php"
echo $folders = ['%LIBRARY_PATH%', '%CACHE_PATH%', '%LIBRARY_PATH%\certs', '%LIBRARY_PATH%\temp']; >> "%XIBO_PATH%\test-installation.php"
echo foreach($folders as $folder) { >> "%XIBO_PATH%\test-installation.php"
echo     $writable = is_writable($folder); >> "%XIBO_PATH%\test-installation.php"
echo     $color = $writable ? 'green' : 'red'; >> "%XIBO_PATH%\test-installation.php"
echo     $status = $writable ? 'OK' : 'ERREUR'; >> "%XIBO_PATH%\test-installation.php"
echo     echo "^<p^>" . basename($folder) . ": ^<span style='color:$color'^>$status^</span^>^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo } >> "%XIBO_PATH%\test-installation.php"
echo echo "^<h2^>Extensions PHP^</h2^>"; >> "%XIBO_PATH%\test-installation.php"
echo $extensions = ['gd', 'pdo_mysql', 'zip', 'curl', 'soap', 'mbstring', 'fileinfo', 'dom', 'xml', 'json']; >> "%XIBO_PATH%\test-installation.php"
echo foreach($extensions as $ext) { >> "%XIBO_PATH%\test-installation.php"
echo     $loaded = extension_loaded($ext); >> "%XIBO_PATH%\test-installation.php"
echo     $color = $loaded ? 'green' : 'red'; >> "%XIBO_PATH%\test-installation.php"
echo     $status = $loaded ? 'OK' : 'MANQUANT'; >> "%XIBO_PATH%\test-installation.php"
echo     echo "^<p^>$ext: ^<span style='color:$color'^>$status^</span^>^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo } >> "%XIBO_PATH%\test-installation.php"
echo echo "^<h2^>Configuration PHP^</h2^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>memory_limit: " . ini_get('memory_limit') . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>upload_max_filesize: " . ini_get('upload_max_filesize') . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>post_max_size: " . ini_get('post_max_size') . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>max_execution_time: " . ini_get('max_execution_time') . "^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<hr^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>^<a href='http://localhost/xibo'^>Lancer l'installation Xibo^</a^>^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^<p^>^<a href='http://xibo.local'^>Accéder via Virtual Host^</a^>^</p^>"; >> "%XIBO_PATH%\test-installation.php"
echo echo "^</body^>^</html^>"; >> "%XIBO_PATH%\test-installation.php"
echo ?^> >> "%XIBO_PATH%\test-installation.php"

echo [CRÉÉ] test-installation.php
echo.

REM Créer un script de maintenance
echo Création du script de maintenance...
echo @echo off > "%XIBO_PATH%\maintenance.bat"
echo REM Script de maintenance Xibo >> "%XIBO_PATH%\maintenance.bat"
echo echo Maintenance Xibo en cours... >> "%XIBO_PATH%\maintenance.bat"
echo. >> "%XIBO_PATH%\maintenance.bat"
echo REM Nettoyer le cache >> "%XIBO_PATH%\maintenance.bat"
echo del /q "%CACHE_PATH%\*.*" 2^>nul >> "%XIBO_PATH%\maintenance.bat"
echo echo Cache nettoyé >> "%XIBO_PATH%\maintenance.bat"
echo. >> "%XIBO_PATH%\maintenance.bat"
echo REM Nettoyer les fichiers temporaires >> "%XIBO_PATH%\maintenance.bat"
echo del /q "%LIBRARY_PATH%\temp\*.*" 2^>nul >> "%XIBO_PATH%\maintenance.bat"
echo echo Fichiers temporaires nettoyés >> "%XIBO_PATH%\maintenance.bat"
echo. >> "%XIBO_PATH%\maintenance.bat"
echo REM Exécuter XTR si disponible >> "%XIBO_PATH%\maintenance.bat"
echo if exist "%XIBO_PATH%\bin\xtr.php" ( >> "%XIBO_PATH%\maintenance.bat"
echo     cd /d "%XIBO_PATH%" >> "%XIBO_PATH%\maintenance.bat"
echo     "%XAMPP_PATH%\php\php.exe" "%XIBO_PATH%\bin\xtr.php" >> "%XIBO_PATH%\maintenance.bat"
echo     echo XTR exécuté >> "%XIBO_PATH%\maintenance.bat"
echo ) >> "%XIBO_PATH%\maintenance.bat"
echo. >> "%XIBO_PATH%\maintenance.bat"
echo echo Maintenance terminée >> "%XIBO_PATH%\maintenance.bat"

echo [CRÉÉ] maintenance.bat
echo.

REM Vérifications finales
echo Vérifications finales...

if exist "%WEB_PATH%\index.php" (
    echo [OK] Fichier principal web/index.php
) else (
    echo [ERREUR] Fichier web/index.php manquant
)

if exist "%WEB_PATH%\.htaccess" (
    echo [OK] Fichier web/.htaccess
) else (
    echo [ATTENTION] Fichier web/.htaccess manquant
)

if exist "%XIBO_PATH%\bin\xtr.php" (
    echo [OK] Script XTR disponible
) else (
    echo [ATTENTION] Script XTR non trouvé
)

echo.

echo ========================================
echo CONFIGURATION FINALE TERMINÉE
echo ========================================
echo.
echo Installation Xibo prête dans: %XIBO_PATH%
echo.
echo URLs de test:
echo - Test complet: http://localhost/xibo/test-installation.php
echo - Installation: http://localhost/xibo
echo - Virtual Host: http://xibo.local
echo.
echo Paramètres base de données:
echo - Host: localhost
echo - Database: xibo_cms
echo - Username: xibo_user
echo - Password: XiboHP2025!
echo.
echo Dossiers configurés:
echo - Library: %LIBRARY_PATH%
echo - Cache: %CACHE_PATH%
echo - Logs: %LIBRARY_PATH%\log
echo.
echo Scripts utiles:
echo - Maintenance: %XIBO_PATH%\maintenance.bat
echo - Test: %XIBO_PATH%\test-installation.php
echo.
echo PROCHAINES ÉTAPES:
echo 1. Démarrer Apache et MySQL dans XAMPP
echo 2. Tester: http://localhost/xibo/test-installation.php
echo 3. Créer la base de données avec create-xibo-database-hp.sql
echo 4. Lancer l'installation: http://localhost/xibo
echo.

pause
