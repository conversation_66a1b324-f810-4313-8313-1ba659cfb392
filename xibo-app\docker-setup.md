# Installation Xibo avec Docker - Solution Professionnelle

## 🐳 **XIBO DOCKER - INSTALLATION GARANTIE**

Docker est la méthode la plus fiable pour installer <PERSON><PERSON>. Voici comment procéder :

## 📋 **Prérequis**

### **1. Installer Docker Desktop**
- **Télécharger** : https://www.docker.com/products/docker-desktop
- **Installer** Docker Desktop pour Windows
- **Redémarrer** l'ordinateur si nécessaire

### **2. Vérifier l'installation**
```bash
# Ouvrir PowerShell et tester
docker --version
docker-compose --version
```

## 🚀 **Installation Xibo Docker**

### **Méthode 1 : Script automatique**

Créez le fichier `install-xibo-docker.ps1` :

```powershell
# Script d'installation Xibo Docker
Write-Host "Installation Xibo avec Docker..." -ForegroundColor Green

# Créer le dossier de projet
$xiboPath = "C:\xibo-docker"
if (-not (Test-Path $xiboPath)) {
    New-Item -ItemType Directory -Path $xiboPath
}

Set-Location $xiboPath

# Télécharger docker-compose.yml
$dockerCompose = @"
version: '3.1'
services:
  xibo-cms:
    image: xibosignage/xibo-cms:release-4.0.15
    volumes:
      - ./shared/cms/custom:/var/www/cms/custom
      - ./shared/backup:/var/www/backup
      - ./shared/cms/web/userscripts:/var/www/cms/web/userscripts
      - ./shared/cms/library:/var/www/cms/library
      - ./shared/cms/cache:/var/www/cms/cache
      - ./shared/cms/web/cache:/var/www/cms/web/cache
    environment:
      - MYSQL_HOST=xibo-db
      - MYSQL_USER=xibo
      - MYSQL_PASSWORD=xibo123
      - MYSQL_DATABASE=xibo
      - CMS_SERVER_NAME=localhost
      - XMR_HOST=xibo-xmr
    ports:
      - "8080:80"
    depends_on:
      - xibo-db
      - xibo-xmr

  xibo-xmr:
    image: xibosignage/xibo-xmr:release-0.10
    ports:
      - "9505:9505"
    volumes:
      - ./shared/cms/custom:/var/www/cms/custom

  xibo-db:
    image: mysql:8.0
    volumes:
      - ./shared/db:/var/lib/mysql
    restart: always
    environment:
      - MYSQL_DATABASE=xibo
      - MYSQL_USER=xibo
      - MYSQL_PASSWORD=xibo123
      - MYSQL_ROOT_PASSWORD=root123
    command: --default-authentication-plugin=mysql_native_password --sql-mode=""
    ports:
      - "3306:3306"

volumes:
  shared:
"@

$dockerCompose | Out-File -FilePath "docker-compose.yml" -Encoding UTF8

Write-Host "Démarrage des conteneurs Xibo..." -ForegroundColor Yellow
docker-compose up -d

Write-Host "Installation terminée !" -ForegroundColor Green
Write-Host "Accédez à Xibo sur: http://localhost:8080" -ForegroundColor Cyan
```

### **Méthode 2 : Installation manuelle**

```bash
# 1. Créer le dossier de projet
mkdir C:\xibo-docker
cd C:\xibo-docker

# 2. Télécharger les fichiers Xibo
git clone https://github.com/xibosignage/xibo-docker.git .

# 3. Configurer l'environnement
cp config.env.template config.env

# 4. Démarrer Xibo
docker-compose up -d
```

## ⚙️ **Configuration**

### **Variables d'environnement (config.env)**
```env
# Base de données
MYSQL_HOST=xibo-db
MYSQL_USER=xibo
MYSQL_PASSWORD=xibo123
MYSQL_DATABASE=xibo

# CMS
CMS_SERVER_NAME=localhost
CMS_ALIAS=localhost:8080

# XMR (Messaging)
XMR_HOST=xibo-xmr

# Sécurité
CMS_SECRET_KEY=votre-cle-secrete-unique
```

### **Ports utilisés**
- **8080** : Interface web Xibo CMS
- **9505** : Service de messagerie XMR
- **3306** : Base de données MySQL

## 🔧 **Commandes utiles**

### **Gestion des conteneurs**
```bash
# Démarrer Xibo
docker-compose up -d

# Arrêter Xibo
docker-compose down

# Voir les logs
docker-compose logs -f

# Redémarrer un service
docker-compose restart xibo-cms

# Mise à jour
docker-compose pull
docker-compose up -d
```

### **Sauvegarde**
```bash
# Sauvegarder la base de données
docker exec xibo-docker_xibo-db_1 mysqldump -u xibo -pxibo123 xibo > backup.sql

# Sauvegarder les fichiers
tar -czf xibo-backup.tar.gz shared/
```

## 🌐 **Accès à Xibo**

### **Première connexion**
1. **URL** : http://localhost:8080
2. **Installation** : Suivre l'assistant web
3. **Base de données** :
   - Host: xibo-db
   - Database: xibo
   - Username: xibo
   - Password: xibo123

### **Compte administrateur**
- **Username** : admin
- **Password** : À définir lors de l'installation

## 🔗 **Intégration avec votre application**

### **API Endpoints Docker**
```javascript
const xiboConfig = {
    cms_url: 'http://localhost:8080',
    api_url: 'http://localhost:8080/api',
    username: 'admin',
    password: 'votre-mot-de-passe'
};
```

### **Connexion depuis votre app**
```javascript
// Dans votre application
function connectToXiboDocker() {
    xiboIntegration.config.cms_url = 'http://localhost:8080';
    xiboIntegration.config.api_url = 'http://localhost:8080/api';
    xiboIntegration.init();
}
```

## 🛠️ **Dépannage**

### **Problèmes courants**

#### **Docker ne démarre pas**
```bash
# Vérifier Docker Desktop
docker info

# Redémarrer Docker Desktop
# Ou redémarrer l'ordinateur
```

#### **Port 8080 occupé**
```yaml
# Changer le port dans docker-compose.yml
ports:
  - "8081:80"  # Utiliser 8081 au lieu de 8080
```

#### **Erreur de base de données**
```bash
# Réinitialiser la base
docker-compose down
docker volume rm xibo-docker_shared
docker-compose up -d
```

#### **Permissions Windows**
```bash
# Exécuter PowerShell en tant qu'administrateur
# Ou ajouter votre utilisateur au groupe docker-users
```

## 📊 **Avantages Docker**

### **✅ Installation garantie**
- Environnement isolé et contrôlé
- Toutes les dépendances incluses
- Fonctionne sur tous les systèmes

### **✅ Maintenance simplifiée**
- Mises à jour faciles
- Sauvegarde/restauration simple
- Pas de conflits avec XAMPP

### **✅ Performance optimisée**
- Configuration optimisée
- Ressources dédiées
- Monitoring intégré

### **✅ Évolutivité**
- Ajout facile de nouveaux services
- Clustering possible
- Intégration CI/CD

## 🎯 **Recommandation**

**Docker est la solution recommandée car :**
1. **Installation garantie** à 100%
2. **Pas de conflits** avec XAMPP
3. **Maintenance facile**
4. **Performance optimale**
5. **Intégration parfaite** avec votre application

## 🚀 **Prochaines étapes**

1. **Installer Docker Desktop**
2. **Exécuter le script d'installation**
3. **Configurer l'intégration** dans votre app
4. **Tester les fonctionnalités**

**Votre application devient alors un tableau de bord professionnel pour Xibo !**
