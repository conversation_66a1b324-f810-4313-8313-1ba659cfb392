# Installation Xibo CMS 4.2.3 avec XAMPP - Configuration HP

## 🎯 Situation actuelle
- **Xibo CMS 4.2.3** déj<PERSON> téléchargé dans `C:\Users\<USER>\Downloads\Prodynamique\xibo-cms-4.2.3`
- **XAMPP** déjà installé sur votre système
- **Scripts de configuration** créés et prêts à utiliser

## 🚀 Installation en 7 étapes simples

### Étape 1: Déplacer et configurer Xibo CMS
```batch
# Clic droit "Exécuter en tant qu'administrateur"
move-configure-xibo.bat
```
**Résultat**: Xibo déplacé vers `C:\xampp\htdocs\xibo` avec permissions configurées

### Étape 2: Configurer XAMPP pour Xibo
```batch
# Clic droit "Exécuter en tant qu'administrateur"
configure-xampp-for-xibo.bat
```
**Résultat**: PHP, Apache et Virtual Host configurés

### Étape 3: Finaliser les permissions
```batch
# Clic droit "Exécuter en tant qu'administrateur"
final-setup-permissions.bat
```
**Résultat**: Sécurité et permissions finalisées

### Étape 4: Démarrer les services XAMPP
1. **Ouvrir**: `C:\xampp\xampp-control.exe`
2. **Démarrer**: Apache (bouton Start)
3. **Démarrer**: MySQL (bouton Start)
4. **Vérifier**: Les deux services sont verts

### Étape 5: Créer la base de données
1. **Accéder**: http://localhost/phpmyadmin
2. **Onglet**: SQL
3. **Copier-coller**: Le contenu de `create-xibo-database-hp.sql`
4. **Exécuter**: Le script

### Étape 6: Installation web Xibo
1. **Navigateur**: http://localhost/xibo
2. **Suivre**: L'assistant d'installation
3. **Utiliser**: Les paramètres de base de données créés

### Étape 7: Configurer les tâches automatiques
```powershell
# PowerShell en tant qu'administrateur
PowerShell -ExecutionPolicy Bypass -File setup-xtr-hp.ps1
```

## 📁 Fichiers créés pour votre installation

### Scripts d'installation
- **`move-configure-xibo.bat`** - Déplace Xibo depuis votre dossier de téléchargement
- **`configure-xampp-for-xibo.bat`** - Configure XAMPP pour Xibo
- **`final-setup-permissions.bat`** - Finalise les permissions et sécurité
- **`setup-xtr-hp.ps1`** - Configure les tâches automatiques

### Base de données
- **`create-xibo-database-hp.sql`** - Script SQL spécifique à votre installation

### Guides détaillés
- **`guide-installation-web-hp.md`** - Guide de l'installation web
- **`test-final-installation-hp.md`** - Tests et validation complète

## ⚙️ Paramètres de votre installation

### Chemins configurés
- **Source**: `C:\Users\<USER>\Downloads\Prodynamique\xibo-cms-4.2.3`
- **Destination**: `C:\xampp\htdocs\xibo`
- **Library**: `C:\xampp\htdocs\xibo\library`
- **Cache**: `C:\xampp\htdocs\xibo\cache`

### Base de données
- **Host**: localhost
- **Database**: xibo_cms
- **Username**: xibo_user
- **Password**: XiboHP2025!

### URLs d'accès
- **Installation**: http://localhost/xibo
- **Virtual Host**: http://xibo.local
- **Test**: http://localhost/xibo/test-installation.php
- **phpMyAdmin**: http://localhost/phpmyadmin

## 🔧 Configuration automatique incluse

### PHP optimisé pour Xibo
```ini
memory_limit = 256M
upload_max_filesize = 128M
post_max_size = 128M
max_execution_time = 300
```

### Extensions PHP activées
- gd, pdo_mysql, zip, curl, soap
- mbstring, fileinfo, dom, xml, json

### Apache configuré
- mod_rewrite activé
- Virtual Host xibo.local
- Permissions optimisées

### Sécurité
- Protection des dossiers sensibles
- Fichiers .htaccess configurés
- Permissions Windows appropriées

## 🧪 Tests de validation

### Tests automatiques inclus
- **`test-installation.php`** - Vérification complète du système
- **`monitor-xtr.ps1`** - Surveillance des tâches automatiques
- **`maintenance.bat`** - Script de maintenance

### Checklist de validation
- [ ] Services XAMPP démarrés
- [ ] Base de données créée
- [ ] Interface web accessible
- [ ] Upload de médias fonctionnel
- [ ] Tâches automatiques actives

## 🔒 Sécurité et maintenance

### Mots de passe configurés
- **MySQL xibo_user**: XiboHP2025!
- **Admin Xibo**: À définir lors de l'installation web

### Tâches automatiques
- **XTR**: Maintenance toutes les minutes
- **Logs**: Nettoyage automatique (30 jours)
- **Surveillance**: Script de monitoring inclus

### Sauvegardes recommandées
```batch
# Base de données
mysqldump -u xibo_user -p xibo_cms > backup_xibo.sql

# Fichiers
xcopy "C:\xampp\htdocs\xibo" "C:\backup\xibo\" /E /I
```

## 🆘 Dépannage rapide

### Problèmes courants
| Problème | Solution |
|----------|----------|
| Apache ne démarre pas | Vérifier le port 80 libre |
| MySQL ne démarre pas | Vérifier le port 3306 libre |
| Page blanche | Consulter les logs Apache |
| Upload impossible | Vérifier permissions library |

### Logs à consulter
- **Apache**: `C:\xampp\apache\logs\error.log`
- **Xibo**: `C:\xampp\htdocs\xibo\library\log\cms.log`
- **XTR**: `C:\xampp\htdocs\xibo\library\log\xtr-maintenance.log`

## 📞 Support et ressources

### Documentation officielle
- **Site**: https://xibosignage.com/
- **Docs**: https://xibosignage.com/docs
- **Forum**: https://community.xibo.org.uk

### Commandes utiles
```batch
# Démarrer XAMPP
C:\xampp\start-xibo.bat

# Tester XTR manuellement
C:\xampp\htdocs\xibo\xibo-xtr-maintenance.bat

# Surveiller XTR
PowerShell -File "C:\xampp\htdocs\xibo\monitor-xtr.ps1"

# Maintenance
C:\xampp\htdocs\xibo\maintenance.bat
```

## 🎉 Après l'installation

### Prochaines étapes
1. **Créer vos premiers layouts** d'affichage
2. **Uploader vos médias** (images, vidéos)
3. **Configurer des displays** virtuels ou réels
4. **Planifier l'affichage** selon vos besoins
5. **Installer des players** Xibo sur vos écrans

### Formation recommandée
- Explorer l'interface d'administration
- Créer des utilisateurs supplémentaires
- Configurer les permissions
- Tester avec différents types de contenu

## 📝 Résumé de l'installation

### Durée estimée: 30-60 minutes
### Niveau: Facile (avec les scripts fournis)
### Résultat: Système d'affichage dynamique professionnel

### ✅ Avantages de cette configuration
- **Installation locale** - Contrôle total
- **Performance optimisée** - Configuration dédiée
- **Sécurité renforcée** - Permissions appropriées
- **Maintenance automatique** - Tâches XTR configurées
- **Support complet** - Scripts et guides inclus

---

## 🚀 Commencer maintenant

**Ordre d'exécution recommandé:**

1. **`move-configure-xibo.bat`** (en admin)
2. **`configure-xampp-for-xibo.bat`** (en admin)
3. **`final-setup-permissions.bat`** (en admin)
4. **Démarrer XAMPP** (Apache + MySQL)
5. **Exécuter** `create-xibo-database-hp.sql` dans phpMyAdmin
6. **Accéder** à http://localhost/xibo pour l'installation web
7. **`setup-xtr-hp.ps1`** (PowerShell en admin)
8. **Tester** avec `test-final-installation-hp.md`

**Temps total**: 30-60 minutes pour une installation complète et fonctionnelle !

---

*Cette installation a été spécialement configurée pour votre environnement HP avec Xibo CMS 4.2.3 et XAMPP. Tous les scripts et paramètres sont optimisés pour votre configuration.*
