# Configuration des tâches automatiques Xibo (XTR)

## Qu'est-ce que XTR ?

**XTR** (Xibo Task Runner) est le planificateur de tâches de Xibo qui gère:
- Maintenance automatique de la base de données
- Envoi d'alertes email
- Archivage des logs
- Synchronisation avec les players
- Nettoyage des fichiers temporaires
- Intégrations tierces

## Prérequis

### Vérifications nécessaires
- [ ] Xibo CMS installé et fonctionnel
- [ ] PHP CLI accessible depuis la ligne de commande
- [ ] Accès administrateur Windows
- [ ] Planificateur de tâches Windows disponible

### Test PHP CLI
```batch
# Ouvrir une invite de commande et tester:
C:\xampp\php\php.exe --version

# Doit afficher la version PHP 8.1+
```

## Méthode 1: Configuration automatique avec script

### Script PowerShell (Recommandé)
Créer le fichier `setup-xtr-task.ps1`:

```powershell
# Script pour configurer automatiquement XTR
param(
    [string]$XamppPath = "C:\xampp",
    [string]$XiboPath = "C:\xampp\htdocs\xibo"
)

# Vérifier les chemins
if (-not (Test-Path "$XamppPath\php\php.exe")) {
    Write-Error "PHP non trouvé dans $XamppPath"
    exit 1
}

if (-not (Test-Path "$XiboPath\bin\xtr.php")) {
    Write-Error "XTR non trouvé dans $XiboPath"
    exit 1
}

# Créer la tâche planifiée
$action = New-ScheduledTaskAction -Execute "$XamppPath\php\php.exe" -Argument "$XiboPath\bin\xtr.php"
$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 1) -RepetitionDuration (New-TimeSpan -Days 365)
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

Register-ScheduledTask -TaskName "Xibo Maintenance (XTR)" -Action $action -Trigger $trigger -Settings $settings -Description "Tâche de maintenance automatique Xibo CMS"

Write-Host "Tâche XTR configurée avec succès!" -ForegroundColor Green
```

### Exécution du script
```batch
# Ouvrir PowerShell en tant qu'administrateur
PowerShell -ExecutionPolicy Bypass -File setup-xtr-task.ps1
```

## Méthode 2: Configuration manuelle

### Étape 1: Créer le script batch
Créer `C:\xampp\htdocs\xibo\xibo-maintenance.bat`:

```batch
@echo off
REM Script de maintenance Xibo XTR
REM Exécuté automatiquement par le Planificateur de tâches

cd /d "C:\xampp\htdocs\xibo"
"C:\xampp\php\php.exe" "C:\xampp\htdocs\xibo\bin\xtr.php"

REM Log de l'exécution (optionnel)
echo %date% %time% - XTR executé >> "C:\xampp\htdocs\xibo\library\log\xtr.log"
```

### Étape 2: Ouvrir le Planificateur de tâches
1. **Windows + R** → `taskschd.msc`
2. Ou **Panneau de configuration** → **Outils d'administration** → **Planificateur de tâches**

### Étape 3: Créer une nouvelle tâche
1. Clic droit sur **Bibliothèque du Planificateur de tâches**
2. **Créer une tâche...**

### Étape 4: Onglet "Général"
- **Nom**: `Xibo Maintenance (XTR)`
- **Description**: `Tâche de maintenance automatique pour Xibo CMS`
- **Sécurité**: 
  - ☑️ Exécuter même si l'utilisateur n'est pas connecté
  - ☑️ Exécuter avec les autorisations maximales
  - **Utilisateur**: Compte administrateur local

### Étape 5: Onglet "Déclencheurs"
1. **Nouveau...**
2. **Paramètres**:
   - Commencer la tâche: **Selon une planification**
   - Quotidien
   - Démarrer: **00:00:00** (minuit)
   - Répéter la tâche toutes les: **1 minute**
   - Pendant: **1 jour**
   - ☑️ Activé

### Étape 6: Onglet "Actions"
1. **Nouveau...**
2. **Action**: Démarrer un programme
3. **Programme/script**: `C:\xampp\php\php.exe`
4. **Ajouter des arguments**: `C:\xampp\htdocs\xibo\bin\xtr.php`
5. **Commencer dans**: `C:\xampp\htdocs\xibo`

### Étape 7: Onglet "Conditions"
- ☑️ Démarrer la tâche seulement si l'ordinateur est inactif pendant: **Décoché**
- ☑️ Arrêter si l'ordinateur n'est plus inactif: **Décoché**
- ☑️ Démarrer la tâche seulement si l'ordinateur fonctionne sur secteur: **Décoché**

### Étape 8: Onglet "Paramètres"
- ☑️ Autoriser l'exécution de la tâche à la demande
- ☑️ Exécuter la tâche dès que possible après un démarrage planifié manqué
- ☑️ Si la tâche échoue, redémarrer toutes les: **1 minute**
- **Tentatives**: 3
- ☑️ Arrêter la tâche si elle s'exécute plus de: **10 minutes**

## Vérification de la configuration

### Test manuel
```batch
# Tester XTR manuellement
cd C:\xampp\htdocs\xibo
C:\xampp\php\php.exe bin\xtr.php

# Doit s'exécuter sans erreur
```

### Vérifier la tâche planifiée
1. **Planificateur de tâches** → **Bibliothèque**
2. Trouver **Xibo Maintenance (XTR)**
3. Clic droit → **Exécuter**
4. Vérifier le **Statut**: "Prêt" après exécution

### Logs de vérification
```batch
# Consulter les logs Xibo
type "C:\xampp\htdocs\xibo\library\log\cms.log"

# Rechercher les entrées XTR
findstr "XTR" "C:\xampp\htdocs\xibo\library\log\cms.log"
```

## Configuration avancée

### Variables d'environnement
Créer un fichier `.env` dans le dossier Xibo:
```env
# Configuration XTR
XTR_LOG_LEVEL=INFO
XTR_MAX_EXECUTION_TIME=300
XTR_MEMORY_LIMIT=256M
```

### Script avec gestion d'erreurs
```batch
@echo off
setlocal

set XIBO_PATH=C:\xampp\htdocs\xibo
set PHP_PATH=C:\xampp\php\php.exe
set LOG_FILE=%XIBO_PATH%\library\log\xtr-task.log

echo %date% %time% - Début XTR >> "%LOG_FILE%"

cd /d "%XIBO_PATH%"
"%PHP_PATH%" "%XIBO_PATH%\bin\xtr.php" >> "%LOG_FILE%" 2>&1

if %ERRORLEVEL% EQU 0 (
    echo %date% %time% - XTR terminé avec succès >> "%LOG_FILE%"
) else (
    echo %date% %time% - XTR terminé avec erreur %ERRORLEVEL% >> "%LOG_FILE%"
)

endlocal
```

## Surveillance et maintenance

### Monitoring des tâches
1. **Planificateur de tâches** → **Historique**
2. Vérifier les exécutions récentes
3. Contrôler les codes de retour

### Logs à surveiller
- `C:\xampp\htdocs\xibo\library\log\cms.log`
- `C:\xampp\htdocs\xibo\library\log\xtr-task.log`
- Logs du Planificateur de tâches Windows

### Alertes recommandées
- Email si XTR échoue 3 fois consécutives
- Notification si la tâche ne s'exécute pas pendant 1 heure
- Alerte espace disque faible

## Dépannage courant

### XTR ne s'exécute pas
```
Vérifications:
1. PHP CLI accessible
2. Permissions du dossier Xibo
3. Tâche planifiée activée
4. Utilisateur avec droits suffisants
```

### Erreurs de permissions
```
Solutions:
1. Exécuter en tant qu'administrateur
2. Vérifier les permissions des dossiers
3. Contrôler l'utilisateur de la tâche
```

### Performance lente
```
Optimisations:
1. Augmenter memory_limit PHP
2. Optimiser la base de données
3. Nettoyer les logs anciens
4. Vérifier l'espace disque
```

### Conflits avec antivirus
```
Exclusions à ajouter:
- C:\xampp\htdocs\xibo\
- C:\xampp\php\php.exe
- Processus XTR
```

## Maintenance préventive

### Tâches hebdomadaires
- Vérifier les logs XTR
- Contrôler l'espace disque
- Nettoyer les fichiers temporaires

### Tâches mensuelles
- Sauvegarder la configuration
- Optimiser la base de données
- Mettre à jour Xibo si nécessaire

### Script de nettoyage automatique
```batch
REM Nettoyer les logs anciens (> 30 jours)
forfiles /p "C:\xampp\htdocs\xibo\library\log" /s /m *.log /d -30 /c "cmd /c del @path"

REM Nettoyer le cache
del /q "C:\xampp\htdocs\xibo\cache\*.*"
```

---

**Important**: XTR est essentiel pour le bon fonctionnement de Xibo. Une configuration correcte garantit la stabilité et les performances du système.
