# Installation Xibo CMS avec XAMPP - Guide Complet

## 🎯 Réponse à votre question

**OUI, il est tout à fait possible d'installer Xibo sur votre PC avec XAMPP !**

Xibo CMS est compatible avec les serveurs web traditionnels comme Apache/MySQL fournis par XAMPP. Ce guide vous accompagne étape par étape pour une installation complète et fonctionnelle.

## 📋 Vue d'ensemble de l'installation

### Prérequis système
- **Windows 10/11** (64-bit recommandé)
- **4 GB RAM minimum** (8 GB recommandé)
- **10 GB d'espace disque libre**
- **Connexion Internet** pour téléchargements

### Composants installés
- **XAMPP** avec PHP 8.1+, Apache, MySQL
- **Xibo CMS** dernière version stable
- **Base de données** MySQL configurée
- **Tâches automatiques** pour la maintenance
- **Interface web** d'administration

## 🚀 Installation rapide (Méthode automatique)

### Étape 1: Préparer XAMPP
1. Télécharger XAMPP depuis https://www.apachefriends.org/
2. Installer avec PHP 8.1 ou supérieur
3. Démarrer Apache et MySQL

### Étape 2: Exécuter le script d'installation
```powershell
# Ouvrir PowerShell en tant qu'administrateur
PowerShell -ExecutionPolicy Bypass -File setup-xibo-xampp.ps1
```

### Étape 3: Finaliser l'installation
1. Télécharger Xibo CMS depuis GitHub
2. Extraire dans `C:\xampp\htdocs\xibo\`
3. Accéder à http://xibo.local pour l'installation web

## 📁 Fichiers fournis dans ce guide

### Scripts d'installation
- **`setup-xibo-xampp.ps1`** - Script PowerShell d'installation automatique
- **`configure-permissions.bat`** - Configuration des permissions Windows
- **`create-xibo-database.sql`** - Script de création de la base de données

### Guides détaillés
- **`install-xibo-xampp.md`** - Guide d'installation complet étape par étape
- **`guide-installation-web.md`** - Guide de l'installation web
- **`configure-xtr-tasks.md`** - Configuration des tâches automatiques
- **`test-installation-xibo.md`** - Tests et validation de l'installation

### Configuration
- **`xampp-config-xibo.txt`** - Paramètres XAMPP pour Xibo
- **`README-Installation-Xibo-XAMPP.md`** - Ce fichier

## 🔧 Installation manuelle détaillée

### 1. Configuration XAMPP
```ini
# Modifications php.ini requises
memory_limit = 256M
upload_max_filesize = 128M
post_max_size = 128M
max_execution_time = 300

# Extensions PHP nécessaires
extension=gd
extension=pdo_mysql
extension=zip
extension=curl
extension=soap
extension=mbstring
```

### 2. Structure des dossiers
```
C:\xampp\htdocs\xibo\
├── web\                 (accessible web)
├── library\             (stockage médias)
├── cache\               (cache temporaire)
├── bin\                 (scripts maintenance)
└── vendor\              (dépendances)
```

### 3. Base de données MySQL
```sql
CREATE DATABASE xibo_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xibo_user'@'localhost' IDENTIFIED BY 'VotreMotDePasse';
GRANT ALL PRIVILEGES ON xibo_cms.* TO 'xibo_user'@'localhost';
```

### 4. Virtual Host Apache (optionnel)
```apache
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/xibo/web"
    ServerName xibo.local
    <Directory "C:/xampp/htdocs/xibo/web">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

## ⚙️ Configuration post-installation

### Tâches automatiques (XTR)
```batch
# Planificateur de tâches Windows
Programme: C:\xampp\php\php.exe
Arguments: C:\xampp\htdocs\xibo\bin\xtr.php
Fréquence: Toutes les minutes
```

### Paramètres recommandés
- **Timezone**: Europe/Paris
- **Language**: Français (si disponible)
- **Library Path**: C:\xampp\htdocs\xibo\library
- **CMS Key**: Généré automatiquement

## 🧪 Tests de validation

### Tests essentiels
1. **Accès CMS**: http://xibo.local
2. **Upload média**: Tester avec une image
3. **Création layout**: Premier contenu d'affichage
4. **Tâches auto**: Vérifier XTR dans les logs

### Checklist de validation
- [ ] Interface web accessible
- [ ] Connexion administrateur fonctionnelle
- [ ] Upload de fichiers opérationnel
- [ ] Base de données connectée
- [ ] Tâches automatiques actives
- [ ] Logs sans erreurs critiques

## 🔒 Sécurité et maintenance

### Sécurité de base
- Mot de passe administrateur fort
- Protection des dossiers sensibles
- Mise à jour régulière de XAMPP
- Sauvegarde de la base de données

### Maintenance automatique
- Nettoyage des logs anciens
- Optimisation de la base de données
- Surveillance de l'espace disque
- Vérification des performances

## 🆘 Dépannage courant

### Problèmes fréquents
| Problème | Solution |
|----------|----------|
| Page blanche | Vérifier les logs Apache/PHP |
| Erreur base de données | Contrôler les identifiants MySQL |
| Upload impossible | Vérifier permissions dossier library |
| XTR ne fonctionne pas | Contrôler la tâche planifiée |

### Logs à consulter
- `C:\xampp\apache\logs\error.log`
- `C:\xampp\htdocs\xibo\library\log\cms.log`
- Planificateur de tâches Windows

## 📞 Support et ressources

### Documentation officielle
- **Site web**: https://xibosignage.com/
- **Documentation**: https://xibosignage.com/docs
- **Forum**: https://community.xibo.org.uk
- **GitHub**: https://github.com/xibosignage/xibo-cms

### Ressources communautaires
- Guides d'installation communautaires
- Templates de layouts
- Intégrations tierces
- Scripts d'automatisation

## 🎉 Prochaines étapes

### Après installation réussie
1. **Configurer les players** Xibo sur vos écrans
2. **Créer vos contenus** (layouts, médias)
3. **Planifier l'affichage** selon vos besoins
4. **Former les utilisateurs** à l'interface
5. **Mettre en place la maintenance** préventive

### Évolutions possibles
- Installation de players sur différents devices
- Intégration avec des sources de données externes
- Personnalisation de l'interface
- Mise en place d'un monitoring avancé

---

## 📝 Résumé des étapes principales

1. **Installer XAMPP** avec PHP 8.1+
2. **Configurer Apache/MySQL** selon les spécifications
3. **Télécharger et extraire Xibo CMS**
4. **Créer la base de données** MySQL
5. **Configurer les permissions** des dossiers
6. **Lancer l'installation web** via navigateur
7. **Configurer les tâches automatiques** XTR
8. **Tester et valider** l'installation

**Durée estimée**: 2-4 heures selon l'expérience

**Niveau de difficulté**: Intermédiaire (avec ce guide: Facile)

**Résultat**: Système d'affichage dynamique professionnel et fonctionnel

---

*Ce guide a été créé pour vous accompagner dans l'installation complète de Xibo avec XAMPP. Suivez les étapes dans l'ordre et n'hésitez pas à consulter les guides détaillés pour chaque section.*
