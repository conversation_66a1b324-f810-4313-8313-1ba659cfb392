# Script d'installation automatique Xibo CMS
# Prend en charge TOUT le processus d'installation
# À exécuter en tant qu'administrateur

param(
    [string]$XamppPath = "C:\xampp",
    [string]$SourcePath = "C:\Users\<USER>\Downloads\xibo"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "INSTALLATION AUTOMATIQUE XIBO CMS" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Fonction pour vérifier les prérequis
function Test-Prerequisites {
    Write-Host "`nVérification des prérequis..." -ForegroundColor Yellow
    
    if (-not (Test-Path $XamppPath)) {
        Write-Host "ERREUR: XAMPP non trouvé dans $XamppPath" -ForegroundColor Red
        return $false
    }
    
    if (-not (Test-Path $SourcePath)) {
        Write-Host "ERREUR: Source Xibo non trouvée dans $SourcePath" -ForegroundColor Red
        return $false
    }
    
    Write-Host "[OK] XAMPP trouvé" -ForegroundColor Green
    Write-Host "[OK] Source Xibo trouvée" -ForegroundColor Green
    return $true
}

# Fonction pour nettoyer l'installation précédente
function Remove-OldInstallation {
    Write-Host "`nNettoyage de l'installation précédente..." -ForegroundColor Yellow
    
    $xiboPath = "$XamppPath\htdocs\xibo"
    if (Test-Path $xiboPath) {
        try {
            Remove-Item $xiboPath -Recurse -Force
            Write-Host "[OK] Ancienne installation supprimée" -ForegroundColor Green
        } catch {
            Write-Host "[ERREUR] Impossible de supprimer l'ancienne installation" -ForegroundColor Red
            Write-Host "Supprimez manuellement: $xiboPath" -ForegroundColor Yellow
            Read-Host "Appuyez sur Entrée quand c'est fait"
        }
    }
}

# Fonction pour copier les fichiers
function Copy-XiboFiles {
    Write-Host "`nCopie des fichiers Xibo..." -ForegroundColor Yellow
    
    $destPath = "$XamppPath\htdocs\xibo"
    
    try {
        # Créer le dossier destination
        New-Item -ItemType Directory -Path $destPath -Force | Out-Null
        
        # Copier tous les fichiers
        Copy-Item "$SourcePath\*" $destPath -Recurse -Force
        
        Write-Host "[OK] Fichiers copiés vers $destPath" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "[ERREUR] Échec de la copie: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour configurer les permissions
function Set-Permissions {
    Write-Host "`nConfiguration des permissions..." -ForegroundColor Yellow
    
    $xiboPath = "$XamppPath\htdocs\xibo"
    
    try {
        # Permissions pour Everyone
        icacls $xiboPath /grant "Everyone:(OI)(CI)F" /T | Out-Null
        
        # Permissions pour IUSR
        icacls $xiboPath /grant "IUSR:(OI)(CI)F" /T | Out-Null
        
        Write-Host "[OK] Permissions configurées" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "[ATTENTION] Erreur permissions: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Fonction pour créer les dossiers nécessaires
function Create-Directories {
    Write-Host "`nCréation des dossiers nécessaires..." -ForegroundColor Yellow
    
    $xiboPath = "$XamppPath\htdocs\xibo"
    $directories = @(
        "$xiboPath\library",
        "$xiboPath\library\temp",
        "$xiboPath\library\screenshots", 
        "$xiboPath\library\playersoftware",
        "$xiboPath\library\certs",
        "$xiboPath\library\log",
        "$xiboPath\cache"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "[CRÉÉ] $dir" -ForegroundColor Green
        }
    }
}

# Fonction pour corriger le fichier .htaccess
function Fix-Htaccess {
    Write-Host "`nCorrection du fichier .htaccess..." -ForegroundColor Yellow
    
    $htaccessPath = "$XamppPath\htdocs\xibo\web\.htaccess"
    
    if (Test-Path $htaccessPath) {
        # Sauvegarder l'original
        Copy-Item $htaccessPath "$htaccessPath.original" -Force
        
        # Créer un .htaccess simple qui fonctionne
        $simpleHtaccess = @"
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
"@
        
        $simpleHtaccess | Set-Content $htaccessPath -Encoding ASCII
        Write-Host "[OK] .htaccess corrigé" -ForegroundColor Green
    }
}

# Fonction pour créer un fichier de test
function Create-TestFile {
    Write-Host "`nCréation du fichier de test..." -ForegroundColor Yellow
    
    $testContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>Test Xibo Installation</title>
</head>
<body>
    <h1>Test Xibo Installation</h1>
    <p>Si vous voyez cette page, l'installation fonctionne !</p>
    <p>Accédez à: <a href="index.php">index.php</a></p>
    <p>Ou: <a href="install/">install/</a></p>
</body>
</html>
"@
    
    $testPath = "$XamppPath\htdocs\xibo\web\test-install.html"
    $testContent | Set-Content $testPath -Encoding UTF8
    
    Write-Host "[OK] Fichier de test créé: test-install.html" -ForegroundColor Green
}

# Fonction pour vérifier les services XAMPP
function Test-XamppServices {
    Write-Host "`nVérification des services XAMPP..." -ForegroundColor Yellow
    
    # Test Apache
    try {
        $response = Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing -TimeoutSec 5
        Write-Host "[OK] Apache fonctionne" -ForegroundColor Green
    } catch {
        Write-Host "[ERREUR] Apache ne répond pas" -ForegroundColor Red
        Write-Host "Démarrez Apache dans le panneau XAMPP" -ForegroundColor Yellow
        return $false
    }
    
    # Test MySQL
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/phpmyadmin" -UseBasicParsing -TimeoutSec 5
        Write-Host "[OK] MySQL/phpMyAdmin accessible" -ForegroundColor Green
    } catch {
        Write-Host "[ERREUR] MySQL/phpMyAdmin ne répond pas" -ForegroundColor Red
        Write-Host "Démarrez MySQL dans le panneau XAMPP" -ForegroundColor Yellow
        return $false
    }
    
    return $true
}

# Fonction principale d'installation
function Install-Xibo {
    Write-Host "`nDémarrage de l'installation automatique..." -ForegroundColor Cyan
    
    # Vérifier les prérequis
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    # Vérifier les services XAMPP
    if (-not (Test-XamppServices)) {
        Write-Host "`nDémarrez Apache et MySQL dans XAMPP puis relancez ce script" -ForegroundColor Yellow
        return $false
    }
    
    # Nettoyer l'ancienne installation
    Remove-OldInstallation
    
    # Copier les fichiers
    if (-not (Copy-XiboFiles)) {
        return $false
    }
    
    # Configurer les permissions
    Set-Permissions
    
    # Créer les dossiers
    Create-Directories
    
    # Corriger .htaccess
    Fix-Htaccess
    
    # Créer le fichier de test
    Create-TestFile
    
    return $true
}

# EXÉCUTION PRINCIPALE
try {
    if (Install-Xibo) {
        Write-Host "`n========================================" -ForegroundColor Green
        Write-Host "INSTALLATION TERMINÉE AVEC SUCCÈS !" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        
        Write-Host "`nTests à effectuer:" -ForegroundColor Cyan
        Write-Host "1. http://localhost/xibo/web/test-install.html" -ForegroundColor White
        Write-Host "2. http://localhost/xibo/web/index.php" -ForegroundColor White
        Write-Host "3. http://localhost/xibo/web/install/" -ForegroundColor White
        
        Write-Host "`nSi les tests fonctionnent, procédez à:" -ForegroundColor Yellow
        Write-Host "• Création de la base de données" -ForegroundColor White
        Write-Host "• Installation web via le navigateur" -ForegroundColor White
        
        # Ouvrir automatiquement le navigateur
        Start-Process "http://localhost/xibo/web/test-install.html"
        
    } else {
        Write-Host "`n[ÉCHEC] L'installation a échoué" -ForegroundColor Red
        Write-Host "Vérifiez les erreurs ci-dessus" -ForegroundColor Yellow
    }
} catch {
    Write-Host "`n[ERREUR CRITIQUE] $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAppuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
