@echo off
REM Script pour configurer les permissions et dossiers Xibo
REM À exécuter en tant qu'administrateur

echo ========================================
echo Configuration des permissions Xibo
echo ========================================

REM Définir les chemins
set XAMPP_PATH=C:\xampp
set XIBO_PATH=%XAMPP_PATH%\htdocs\xibo
set LIBRARY_PATH=%XIBO_PATH%\library
set CACHE_PATH=%XIBO_PATH%\cache
set CERTS_PATH=%LIBRARY_PATH%\certs
set WEB_PATH=%XIBO_PATH%\web

echo Chemins configurés:
echo - XAMPP: %XAMPP_PATH%
echo - Xibo: %XIBO_PATH%
echo - Library: %LIBRARY_PATH%
echo - Cache: %CACHE_PATH%
echo - Certificats: %CERTS_PATH%
echo - Web: %WEB_PATH%
echo.

REM Vérifier si XAMPP existe
if not exist "%XAMPP_PATH%" (
    echo ERREUR: XAMPP non trouvé dans %XAMPP_PATH%
    echo Veuillez installer XAMPP d'abord
    pause
    exit /b 1
)

REM Créer les dossiers principaux
echo Création des dossiers...
if not exist "%XIBO_PATH%" mkdir "%XIBO_PATH%"
if not exist "%LIBRARY_PATH%" mkdir "%LIBRARY_PATH%"
if not exist "%CACHE_PATH%" mkdir "%CACHE_PATH%"
if not exist "%CERTS_PATH%" mkdir "%CERTS_PATH%"
if not exist "%WEB_PATH%" mkdir "%WEB_PATH%"

REM Créer les sous-dossiers de library
if not exist "%LIBRARY_PATH%\temp" mkdir "%LIBRARY_PATH%\temp"
if not exist "%LIBRARY_PATH%\screenshots" mkdir "%LIBRARY_PATH%\screenshots"
if not exist "%LIBRARY_PATH%\playersoftware" mkdir "%LIBRARY_PATH%\playersoftware"

echo Dossiers créés avec succès.
echo.

REM Configurer les permissions Windows
echo Configuration des permissions...

REM Donner les permissions complètes à l'utilisateur actuel
icacls "%XIBO_PATH%" /grant "%USERNAME%:(OI)(CI)F" /T

REM Donner les permissions à IUSR (utilisateur IIS)
icacls "%XIBO_PATH%" /grant "IUSR:(OI)(CI)F" /T

REM Donner les permissions à IIS_IUSRS
icacls "%XIBO_PATH%" /grant "IIS_IUSRS:(OI)(CI)F" /T

REM Permissions spéciales pour les dossiers sensibles
icacls "%LIBRARY_PATH%" /grant "Everyone:(OI)(CI)F" /T
icacls "%CACHE_PATH%" /grant "Everyone:(OI)(CI)F" /T

echo Permissions configurées.
echo.

REM Créer les fichiers de configuration de base
echo Création des fichiers de configuration...

REM Créer un fichier .htaccess de base pour le dossier library
echo # Protéger le dossier library > "%LIBRARY_PATH%\.htaccess"
echo Order Deny,Allow >> "%LIBRARY_PATH%\.htaccess"
echo Deny from all >> "%LIBRARY_PATH%\.htaccess"
echo # Autoriser seulement les fichiers médias >> "%LIBRARY_PATH%\.htaccess"
echo ^<Files ~ "\.(jpg|jpeg|png|gif|mp4|avi|pdf|ppt|pptx)$"^> >> "%LIBRARY_PATH%\.htaccess"
echo Allow from all >> "%LIBRARY_PATH%\.htaccess"
echo ^</Files^> >> "%LIBRARY_PATH%\.htaccess"

REM Créer un fichier index.php de protection
echo ^<?php > "%LIBRARY_PATH%\index.php"
echo // Protection du dossier library >> "%LIBRARY_PATH%\index.php"
echo header('HTTP/1.0 403 Forbidden'); >> "%LIBRARY_PATH%\index.php"
echo exit('Accès interdit'); >> "%LIBRARY_PATH%\index.php"
echo ?^> >> "%LIBRARY_PATH%\index.php"

REM Créer un fichier de test
echo ^<?php > "%XIBO_PATH%\test-permissions.php"
echo echo "Test des permissions Xibo^<br^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "Dossier library: " . (is_writable('%LIBRARY_PATH%') ? 'OK' : 'ERREUR') . "^<br^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "Dossier cache: " . (is_writable('%CACHE_PATH%') ? 'OK' : 'ERREUR') . "^<br^>"; >> "%XIBO_PATH%\test-permissions.php"
echo echo "Dossier certs: " . (is_writable('%CERTS_PATH%') ? 'OK' : 'ERREUR') . "^<br^>"; >> "%XIBO_PATH%\test-permissions.php"
echo ?^> >> "%XIBO_PATH%\test-permissions.php"

echo Fichiers de configuration créés.
echo.

REM Créer un script de nettoyage
echo @echo off > "%XIBO_PATH%\cleanup.bat"
echo REM Script de nettoyage Xibo >> "%XIBO_PATH%\cleanup.bat"
echo echo Nettoyage des fichiers temporaires... >> "%XIBO_PATH%\cleanup.bat"
echo del /q "%CACHE_PATH%\*.*" 2^>nul >> "%XIBO_PATH%\cleanup.bat"
echo del /q "%LIBRARY_PATH%\temp\*.*" 2^>nul >> "%XIBO_PATH%\cleanup.bat"
echo echo Nettoyage terminé. >> "%XIBO_PATH%\cleanup.bat"

REM Afficher les informations de vérification
echo ========================================
echo VÉRIFICATIONS
echo ========================================

echo Vérification des dossiers:
if exist "%XIBO_PATH%" (echo [OK] Dossier Xibo) else (echo [ERREUR] Dossier Xibo manquant)
if exist "%LIBRARY_PATH%" (echo [OK] Dossier Library) else (echo [ERREUR] Dossier Library manquant)
if exist "%CACHE_PATH%" (echo [OK] Dossier Cache) else (echo [ERREUR] Dossier Cache manquant)
if exist "%CERTS_PATH%" (echo [OK] Dossier Certificats) else (echo [ERREUR] Dossier Certificats manquant)
if exist "%WEB_PATH%" (echo [OK] Dossier Web) else (echo [ERREUR] Dossier Web manquant)

echo.
echo Vérification des permissions:
icacls "%LIBRARY_PATH%" | findstr /C:"Everyone" >nul && echo [OK] Permissions Library || echo [ATTENTION] Vérifier permissions Library
icacls "%CACHE_PATH%" | findstr /C:"Everyone" >nul && echo [OK] Permissions Cache || echo [ATTENTION] Vérifier permissions Cache

echo.
echo ========================================
echo CONFIGURATION TERMINÉE
echo ========================================
echo.
echo Prochaines étapes:
echo 1. Extraire l'archive Xibo dans: %XIBO_PATH%
echo 2. Démarrer Apache et MySQL dans XAMPP
echo 3. Créer la base de données avec: create-xibo-database.sql
echo 4. Accéder à: http://xibo.local pour l'installation
echo 5. Tester les permissions: http://localhost/xibo/test-permissions.php
echo.
echo Fichiers utiles créés:
echo - %XIBO_PATH%\test-permissions.php (test des permissions)
echo - %XIBO_PATH%\cleanup.bat (nettoyage)
echo - %LIBRARY_PATH%\.htaccess (sécurité)
echo.

pause
