// Application Xibo Display Manager
// Gestion complète de l'affichage dynamique

class XiboDisplayManager {
    constructor() {
        this.displays = [];
        this.media = [];
        this.layouts = [];
        this.events = [];
        this.init();
    }

    init() {
        this.loadSampleData();
        this.updateDashboard();
        this.startRealTimeUpdates();
        console.log('Xibo Display Manager initialisé');
    }

    // Charger des données d'exemple
    loadSampleData() {
        // Écrans d'exemple
        this.displays = [
            {
                id: 1,
                name: "Écran Accueil",
                location: "Hall d'entrée",
                status: "online",
                lastSeen: new Date(),
                currentLayout: "Bienvenue HP"
            },
            {
                id: 2,
                name: "Écran Cafétéria",
                location: "Espace restauration",
                status: "online",
                lastSeen: new Date(),
                currentLayout: "Menu du jour"
            },
            {
                id: 3,
                name: "Écran Réunion",
                location: "Salle de conférence",
                status: "warning",
                lastSeen: new Date(Date.now() - 300000),
                currentLayout: "Planning réunions"
            }
        ];

        // Médias d'exemple
        this.media = [
            {
                id: 1,
                name: "logo-hp.png",
                type: "image",
                size: "2.3 MB",
                uploaded: "2025-01-07",
                thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+SU1BR0U8L3RleHQ+PC9zdmc+"
            },
            {
                id: 2,
                name: "presentation.mp4",
                type: "video",
                size: "45.7 MB",
                uploaded: "2025-01-07",
                thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2ZmNjk5NCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VklERU88L3RleHQ+PC9zdmc+"
            },
            {
                id: 3,
                name: "actualites.rss",
                type: "rss",
                size: "1.2 KB",
                uploaded: "2025-01-07",
                thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2ZmOTgwMCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+UlNTPC90ZXh0Pjwvc3ZnPg=="
            }
        ];

        // Layouts d'exemple
        this.layouts = [
            {
                id: 1,
                name: "Bienvenue HP",
                description: "Layout d'accueil avec logo et message",
                regions: 3,
                duration: 30,
                status: "published"
            },
            {
                id: 2,
                name: "Menu du jour",
                description: "Affichage du menu de la cafétéria",
                regions: 2,
                duration: 60,
                status: "published"
            },
            {
                id: 3,
                name: "Planning réunions",
                description: "Calendrier des réunions en cours",
                regions: 1,
                duration: 120,
                status: "draft"
            }
        ];
    }

    // Mettre à jour le tableau de bord
    updateDashboard() {
        document.getElementById('active-displays').textContent = this.displays.filter(d => d.status === 'online').length;
        document.getElementById('media-count').textContent = this.media.length;
        document.getElementById('layout-count').textContent = this.layouts.length;
        document.getElementById('event-count').textContent = this.events.length;
    }

    // Afficher une section
    showSection(sectionName) {
        // Cacher toutes les sections
        document.querySelectorAll('.section').forEach(section => {
            section.style.display = 'none';
        });

        // Afficher la section demandée
        document.getElementById(sectionName).style.display = 'block';

        // Mettre à jour la navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[href="#${sectionName}"]`).classList.add('active');

        // Charger le contenu spécifique
        switch(sectionName) {
            case 'displays':
                this.loadDisplays();
                break;
            case 'media':
                this.loadMedia();
                break;
            case 'layouts':
                this.loadLayouts();
                break;
            case 'schedule':
                this.loadSchedule();
                break;
        }
    }

    // Charger les écrans
    loadDisplays() {
        const container = document.getElementById('displays-container');
        container.innerHTML = '';

        this.displays.forEach(display => {
            const statusClass = display.status === 'online' ? 'status-online' : 
                               display.status === 'warning' ? 'status-warning' : 'status-offline';
            
            const displayCard = `
                <div class="col-md-4 mb-3">
                    <div class="feature-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6><i class="fas fa-desktop"></i> ${display.name}</h6>
                            <span class="status-indicator ${statusClass}"></span>
                        </div>
                        <p class="text-muted mb-2"><i class="fas fa-map-marker-alt"></i> ${display.location}</p>
                        <p class="mb-2"><strong>Layout actuel:</strong> ${display.currentLayout}</p>
                        <p class="mb-3"><strong>Dernière activité:</strong> ${this.formatTime(display.lastSeen)}</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="xiboManager.editDisplay(${display.id})">
                                <i class="fas fa-edit"></i> Modifier
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="xiboManager.sendToDisplay(${display.id})">
                                <i class="fas fa-paper-plane"></i> Envoyer contenu
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += displayCard;
        });
    }

    // Charger les médias
    loadMedia() {
        const container = document.getElementById('media-container');
        container.innerHTML = '';

        this.media.forEach(media => {
            const mediaItem = `
                <div class="media-item" onclick="xiboManager.previewMedia(${media.id})">
                    <img src="${media.thumbnail}" alt="${media.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 5px;">
                    <h6 class="mt-2 mb-1">${media.name}</h6>
                    <small class="text-muted">${media.type.toUpperCase()}</small><br>
                    <small class="text-muted">${media.size}</small>
                </div>
            `;
            container.innerHTML += mediaItem;
        });

        // Ajouter le bouton d'upload
        container.innerHTML += `
            <div class="media-item" onclick="xiboManager.uploadMedia()" style="cursor: pointer;">
                <i class="fas fa-plus fa-3x text-muted mb-3"></i>
                <h6>Ajouter un média</h6>
                <small class="text-muted">Cliquez pour uploader</small>
            </div>
        `;
    }

    // Charger les layouts
    loadLayouts() {
        const container = document.getElementById('layouts-container');
        container.innerHTML = '';

        this.layouts.forEach(layout => {
            const statusBadge = layout.status === 'published' ? 'bg-success' : 'bg-warning';
            
            const layoutCard = `
                <div class="col-md-4 mb-3">
                    <div class="feature-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6><i class="fas fa-th-large"></i> ${layout.name}</h6>
                            <span class="badge ${statusBadge}">${layout.status}</span>
                        </div>
                        <p class="text-muted mb-2">${layout.description}</p>
                        <p class="mb-2"><strong>Régions:</strong> ${layout.regions}</p>
                        <p class="mb-3"><strong>Durée:</strong> ${layout.duration}s</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="xiboManager.editLayout(${layout.id})">
                                <i class="fas fa-edit"></i> Modifier
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="xiboManager.previewLayout(${layout.id})">
                                <i class="fas fa-eye"></i> Aperçu
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += layoutCard;
        });
    }

    // Charger la planification
    loadSchedule() {
        const container = document.getElementById('calendar-container');
        container.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-calendar-alt fa-4x text-muted mb-3"></i>
                <h5>Calendrier de planification</h5>
                <p class="text-muted">Planifiez vos contenus sur vos écrans</p>
                <button class="btn btn-custom" onclick="xiboManager.scheduleContent()">
                    <i class="fas fa-plus"></i> Nouvel événement
                </button>
            </div>
        `;
    }

    // Actions
    createLayout() {
        alert('Fonctionnalité: Créer un nouveau layout\n\nCette fonction ouvrirait un éditeur de layout avec glisser-déposer pour créer vos affichages personnalisés.');
    }

    uploadMedia() {
        alert('Fonctionnalité: Upload de média\n\nCette fonction permettrait d\'uploader des images, vidéos, documents PDF, flux RSS, etc.');
    }

    scheduleContent() {
        alert('Fonctionnalité: Planification\n\nCette fonction permettrait de programmer l\'affichage de vos contenus selon des horaires précis.');
    }

    addDisplay() {
        const name = prompt('Nom du nouvel écran:');
        if (name) {
            const newDisplay = {
                id: this.displays.length + 1,
                name: name,
                location: 'À définir',
                status: 'offline',
                lastSeen: new Date(),
                currentLayout: 'Aucun'
            };
            this.displays.push(newDisplay);
            this.loadDisplays();
            this.updateDashboard();
        }
    }

    editDisplay(id) {
        const display = this.displays.find(d => d.id === id);
        alert(`Modification de l'écran: ${display.name}\n\nCette fonction ouvrirait un formulaire de configuration pour l'écran.`);
    }

    sendToDisplay(id) {
        const display = this.displays.find(d => d.id === id);
        alert(`Envoi de contenu vers: ${display.name}\n\nCette fonction permettrait de sélectionner un layout à afficher immédiatement.`);
    }

    previewMedia(id) {
        const media = this.media.find(m => m.id === id);
        alert(`Aperçu du média: ${media.name}\n\nCette fonction afficherait un aperçu du média sélectionné.`);
    }

    editLayout(id) {
        const layout = this.layouts.find(l => l.id === id);
        alert(`Modification du layout: ${layout.name}\n\nCette fonction ouvrirait l'éditeur de layout.`);
    }

    previewLayout(id) {
        const layout = this.layouts.find(l => l.id === id);
        alert(`Aperçu du layout: ${layout.name}\n\nCette fonction afficherait un aperçu du layout en plein écran.`);
    }

    viewReports() {
        alert('Fonctionnalité: Rapports\n\nCette fonction afficherait des statistiques détaillées sur l\'utilisation de vos écrans.');
    }

    // Utilitaires
    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        
        if (minutes < 1) return 'À l\'instant';
        if (minutes < 60) return `Il y a ${minutes} min`;
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `Il y a ${hours}h`;
        
        return date.toLocaleDateString();
    }

    // Mises à jour en temps réel
    startRealTimeUpdates() {
        setInterval(() => {
            this.addActivityLog();
        }, 5000);
    }

    addActivityLog() {
        const activities = [
            'Synchronisation des écrans réussie',
            'Nouveau contenu déployé',
            'Mise à jour automatique effectuée',
            'Vérification de l\'état des écrans',
            'Sauvegarde des données terminée'
        ];
        
        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        const timestamp = new Date().toLocaleString();
        
        const logContainer = document.getElementById('activity-log');
        const newLog = document.createElement('div');
        newLog.textContent = `[${timestamp}] Système: ${randomActivity}`;
        
        logContainer.insertBefore(newLog, logContainer.firstChild);
        
        // Garder seulement les 10 dernières entrées
        while (logContainer.children.length > 10) {
            logContainer.removeChild(logContainer.lastChild);
        }
    }
}

// Fonctions globales
function showSection(sectionName) {
    xiboManager.showSection(sectionName);
}

function createLayout() {
    xiboManager.createLayout();
}

function uploadMedia() {
    xiboManager.uploadMedia();
}

function scheduleContent() {
    xiboManager.scheduleContent();
}

function addDisplay() {
    xiboManager.addDisplay();
}

function viewReports() {
    xiboManager.viewReports();
}

// Initialiser l'application
let xiboManager;
document.addEventListener('DOMContentLoaded', function() {
    xiboManager = new XiboDisplayManager();
});
