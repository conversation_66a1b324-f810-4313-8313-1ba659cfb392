<?php
/**
 * API Professionnelle Xibo Integration
 * Connexion directe avec Xibo CMS Docker
 * Gestion complète de l'affichage dynamique
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

class XiboProfessionalAPI {
    private $config;
    private $token;
    private $tokenExpiry;
    
    public function __construct() {
        $this->config = [
            'xibo_url' => 'http://localhost:8080',
            'api_url' => 'http://localhost:8080/api',
            'client_id' => 'xibo_client',
            'client_secret' => 'xibo_secret',
            'username' => 'admin',
            'password' => 'admin',
            'db_host' => 'localhost',
            'db_port' => '3307',
            'db_name' => 'xibo_cms',
            'db_user' => 'xibo_user',
            'db_pass' => 'XiboHP2025!'
        ];
        
        $this->authenticate();
    }
    
    private function authenticate() {
        // Essayer l'authentification API Xibo
        $authData = [
            'grant_type' => 'password',
            'client_id' => $this->config['client_id'],
            'client_secret' => $this->config['client_secret'],
            'username' => $this->config['username'],
            'password' => $this->config['password']
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->config['api_url'] . '/authorize/access_token');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($authData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 && $response) {
            $tokenData = json_decode($response, true);
            if (isset($tokenData['access_token'])) {
                $this->token = $tokenData['access_token'];
                $this->tokenExpiry = time() + $tokenData['expires_in'];
                return true;
            }
        }
        
        // Si l'API échoue, utiliser la base de données directement
        return $this->connectDatabase();
    }
    
    private function connectDatabase() {
        try {
            $dsn = "mysql:host={$this->config['db_host']};port={$this->config['db_port']};dbname={$this->config['db_name']};charset=utf8mb4";
            $this->db = new PDO($dsn, $this->config['db_user'], $this->config['db_pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            return true;
        } catch (PDOException $e) {
            error_log("Erreur connexion DB: " . $e->getMessage());
            return false;
        }
    }
    
    private function apiCall($endpoint, $method = 'GET', $data = null) {
        if (!$this->token || time() >= $this->tokenExpiry) {
            $this->authenticate();
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->config['api_url'] . $endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json'
        ]);
        
        if ($method !== 'GET') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return json_decode($response, true);
        }
        
        return null;
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_GET['endpoint'] ?? '';
        
        try {
            switch ($path) {
                case 'status':
                    $this->getSystemStatus();
                    break;
                case 'displays':
                    $this->handleDisplays($method);
                    break;
                case 'media':
                    $this->handleMedia($method);
                    break;
                case 'layouts':
                    $this->handleLayouts($method);
                    break;
                case 'schedule':
                    $this->handleSchedule($method);
                    break;
                case 'stats':
                    $this->getStatistics();
                    break;
                case 'upload':
                    $this->handleUpload();
                    break;
                default:
                    $this->sendResponse(['error' => 'Endpoint non trouvé'], 404);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    private function getSystemStatus() {
        $status = [
            'xibo_cms' => $this->checkXiboStatus(),
            'database' => $this->checkDatabaseStatus(),
            'docker' => $this->checkDockerStatus(),
            'api' => $this->token ? 'connected' : 'disconnected',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $this->sendResponse($status);
    }
    
    private function checkXiboStatus() {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->config['xibo_url'] . '/login');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return ($httpCode === 200) ? 'online' : 'offline';
    }
    
    private function checkDatabaseStatus() {
        if (isset($this->db)) {
            try {
                $this->db->query('SELECT 1');
                return 'connected';
            } catch (PDOException $e) {
                return 'error';
            }
        }
        return 'disconnected';
    }
    
    private function checkDockerStatus() {
        $containers = ['xibo-cms-hp', 'xibo-db-hp', 'xibo-xmr-hp'];
        $status = [];
        
        foreach ($containers as $container) {
            $output = shell_exec("docker inspect --format='{{.State.Status}}' $container 2>/dev/null");
            $status[$container] = trim($output) === 'running' ? 'running' : 'stopped';
        }
        
        return $status;
    }
    
    private function handleDisplays($method) {
        switch ($method) {
            case 'GET':
                $displays = $this->apiCall('/display') ?? $this->getDisplaysFromDB();
                $this->sendResponse($displays);
                break;
            case 'POST':
                $data = json_decode(file_get_contents('php://input'), true);
                $result = $this->createDisplay($data);
                $this->sendResponse($result);
                break;
            default:
                $this->sendResponse(['error' => 'Méthode non supportée'], 405);
        }
    }
    
    private function getDisplaysFromDB() {
        if (!isset($this->db)) return ['data' => []];
        
        try {
            $stmt = $this->db->query("
                SELECT displayId, display, description, defaultLayoutId, 
                       licensed, loggedIn, lastAccessed, clientAddress,
                       screenShotRequested, storageAvailableSpace, storageTotalSpace
                FROM display 
                ORDER BY display
            ");
            
            return ['data' => $stmt->fetchAll()];
        } catch (PDOException $e) {
            return ['data' => []];
        }
    }
    
    private function handleMedia($method) {
        switch ($method) {
            case 'GET':
                $media = $this->apiCall('/library') ?? $this->getMediaFromDB();
                $this->sendResponse($media);
                break;
            default:
                $this->sendResponse(['error' => 'Méthode non supportée'], 405);
        }
    }
    
    private function getMediaFromDB() {
        if (!isset($this->db)) return ['data' => []];
        
        try {
            $stmt = $this->db->query("
                SELECT mediaId, name, type, duration, fileSize, 
                       storedAs, createdDt, modifiedDt, userId
                FROM media 
                WHERE retired = 0
                ORDER BY createdDt DESC
            ");
            
            return ['data' => $stmt->fetchAll()];
        } catch (PDOException $e) {
            return ['data' => []];
        }
    }
    
    private function handleLayouts($method) {
        switch ($method) {
            case 'GET':
                $layouts = $this->apiCall('/layout') ?? $this->getLayoutsFromDB();
                $this->sendResponse($layouts);
                break;
            default:
                $this->sendResponse(['error' => 'Méthode non supportée'], 405);
        }
    }
    
    private function getLayoutsFromDB() {
        if (!isset($this->db)) return ['data' => []];
        
        try {
            $stmt = $this->db->query("
                SELECT layoutId, layout, description, duration, 
                       status, userId, createdDt, modifiedDt,
                       width, height, backgroundColor
                FROM layout 
                WHERE retired = 0
                ORDER BY layout
            ");
            
            return ['data' => $stmt->fetchAll()];
        } catch (PDOException $e) {
            return ['data' => []];
        }
    }
    
    private function handleSchedule($method) {
        switch ($method) {
            case 'GET':
                $schedule = $this->getScheduleFromDB();
                $this->sendResponse($schedule);
                break;
            default:
                $this->sendResponse(['error' => 'Méthode non supportée'], 405);
        }
    }
    
    private function getScheduleFromDB() {
        if (!isset($this->db)) return ['data' => []];
        
        try {
            $stmt = $this->db->query("
                SELECT s.eventId, s.displayId, s.layoutId, s.fromDt, s.toDt,
                       s.priority, s.userId, s.createdDt,
                       d.display as displayName, l.layout as layoutName
                FROM schedule s
                LEFT JOIN display d ON s.displayId = d.displayId
                LEFT JOIN layout l ON s.layoutId = l.layoutId
                WHERE s.fromDt >= NOW() - INTERVAL 7 DAY
                ORDER BY s.fromDt
            ");
            
            return ['data' => $stmt->fetchAll()];
        } catch (PDOException $e) {
            return ['data' => []];
        }
    }
    
    private function getStatistics() {
        $stats = [
            'displays' => [
                'total' => 0,
                'online' => 0,
                'offline' => 0
            ],
            'media' => [
                'total' => 0,
                'size_total' => 0
            ],
            'layouts' => [
                'total' => 0,
                'published' => 0
            ],
            'schedule' => [
                'active_events' => 0,
                'upcoming_events' => 0
            ]
        ];
        
        if (isset($this->db)) {
            try {
                // Statistiques displays
                $stmt = $this->db->query("SELECT COUNT(*) as total, SUM(loggedIn) as online FROM display");
                $displayStats = $stmt->fetch();
                $stats['displays']['total'] = (int)$displayStats['total'];
                $stats['displays']['online'] = (int)$displayStats['online'];
                $stats['displays']['offline'] = $stats['displays']['total'] - $stats['displays']['online'];
                
                // Statistiques médias
                $stmt = $this->db->query("SELECT COUNT(*) as total, SUM(fileSize) as size FROM media WHERE retired = 0");
                $mediaStats = $stmt->fetch();
                $stats['media']['total'] = (int)$mediaStats['total'];
                $stats['media']['size_total'] = (int)$mediaStats['size'];
                
                // Statistiques layouts
                $stmt = $this->db->query("SELECT COUNT(*) as total, SUM(status) as published FROM layout WHERE retired = 0");
                $layoutStats = $stmt->fetch();
                $stats['layouts']['total'] = (int)$layoutStats['total'];
                $stats['layouts']['published'] = (int)$layoutStats['published'];
                
                // Statistiques planning
                $stmt = $this->db->query("
                    SELECT 
                        SUM(CASE WHEN fromDt <= NOW() AND toDt >= NOW() THEN 1 ELSE 0 END) as active,
                        SUM(CASE WHEN fromDt > NOW() THEN 1 ELSE 0 END) as upcoming
                    FROM schedule
                ");
                $scheduleStats = $stmt->fetch();
                $stats['schedule']['active_events'] = (int)$scheduleStats['active'];
                $stats['schedule']['upcoming_events'] = (int)$scheduleStats['upcoming'];
                
            } catch (PDOException $e) {
                // Garder les valeurs par défaut
            }
        }
        
        $this->sendResponse($stats);
    }
    
    private function handleUpload() {
        if (!isset($_FILES['file'])) {
            $this->sendResponse(['error' => 'Aucun fichier'], 400);
            return;
        }
        
        $file = $_FILES['file'];
        $uploadDir = 'uploads/';
        
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filename = uniqid() . '_' . basename($file['name']);
        $filepath = $uploadDir . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $this->sendResponse([
                'success' => true,
                'filename' => $filename,
                'size' => $file['size'],
                'type' => $file['type']
            ]);
        } else {
            $this->sendResponse(['error' => 'Échec upload'], 500);
        }
    }
    
    private function sendResponse($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
}

// Point d'entrée
$api = new XiboProfessionalAPI();
$api->handleRequest();
?>
