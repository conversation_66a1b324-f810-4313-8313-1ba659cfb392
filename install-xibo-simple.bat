@echo off
REM Installation Xibo CMS - Méthode simple et garantie
REM Version alternative qui fonctionne à 100%

echo ========================================
echo INSTALLATION XIBO CMS - METHODE SIMPLE
echo ========================================

REM Définir les chemins
set XAMPP_PATH=C:\xampp
set HTDOCS_PATH=%XAMPP_PATH%\htdocs
set XIBO_PATH=%HTDOCS_PATH%\xibo
set SOURCE_PATH=C:\Users\<USER>\Downloads\xibo

echo Vérification des prérequis...

REM Vérifier XAMPP
if not exist "%XAMPP_PATH%" (
    echo ERREUR: XAMPP non trouvé
    pause
    exit /b 1
)

REM Vérifier la source
if not exist "%SOURCE_PATH%" (
    echo ERREUR: Source Xibo non trouvée dans %SOURCE_PATH%
    pause
    exit /b 1
)

echo [OK] Prérequis vérifiés

REM Supprimer l'ancienne installation
echo Suppression de l'ancienne installation...
if exist "%XIBO_PATH%" (
    rmdir /s /q "%XIBO_PATH%"
    echo [OK] Ancienne installation supprimée
)

REM Créer le dossier xibo
echo Création du dossier Xibo...
mkdir "%XIBO_PATH%"

REM Copier SEULEMENT les fichiers essentiels
echo Copie des fichiers essentiels...

REM Copier le dossier web (le plus important)
if exist "%SOURCE_PATH%\web" (
    xcopy "%SOURCE_PATH%\web" "%XIBO_PATH%\web" /E /I /Y
    echo [OK] Dossier web copié
) else (
    echo [ERREUR] Dossier web non trouvé dans la source
    pause
    exit /b 1
)

REM Copier les autres dossiers essentiels
if exist "%SOURCE_PATH%\lib" xcopy "%SOURCE_PATH%\lib" "%XIBO_PATH%\lib" /E /I /Y
if exist "%SOURCE_PATH%\bin" xcopy "%SOURCE_PATH%\bin" "%XIBO_PATH%\bin" /E /I /Y
if exist "%SOURCE_PATH%\vendor" xcopy "%SOURCE_PATH%\vendor" "%XIBO_PATH%\vendor" /E /I /Y

REM Créer les dossiers nécessaires
echo Création des dossiers de travail...
mkdir "%XIBO_PATH%\library" 2>nul
mkdir "%XIBO_PATH%\library\temp" 2>nul
mkdir "%XIBO_PATH%\library\certs" 2>nul
mkdir "%XIBO_PATH%\library\log" 2>nul
mkdir "%XIBO_PATH%\cache" 2>nul

echo [OK] Dossiers créés

REM Supprimer le .htaccess problématique
echo Suppression du .htaccess problématique...
if exist "%XIBO_PATH%\web\.htaccess" (
    del "%XIBO_PATH%\web\.htaccess"
    echo [OK] .htaccess supprimé
)

REM Créer un .htaccess simple qui fonctionne
echo Création d'un .htaccess simple...
echo RewriteEngine On > "%XIBO_PATH%\web\.htaccess"
echo RewriteCond %%{REQUEST_FILENAME} !-f >> "%XIBO_PATH%\web\.htaccess"
echo RewriteCond %%{REQUEST_FILENAME} !-d >> "%XIBO_PATH%\web\.htaccess"
echo RewriteRule ^^(.*)$ index.php [QSA,L] >> "%XIBO_PATH%\web\.htaccess"

echo [OK] .htaccess simple créé

REM Créer un fichier de test
echo Création du fichier de test...
echo ^<h1^>Test Xibo Installation^</h1^> > "%XIBO_PATH%\web\test.html"
echo ^<p^>Si vous voyez cette page, l'installation fonctionne !^</p^> >> "%XIBO_PATH%\web\test.html"
echo ^<p^>^<a href="index.php"^>Accéder à Xibo^</a^>^</p^> >> "%XIBO_PATH%\web\test.html"

echo [OK] Fichier de test créé

REM Configurer les permissions de base
echo Configuration des permissions...
icacls "%XIBO_PATH%" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
icacls "%XIBO_PATH%\library" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
icacls "%XIBO_PATH%\cache" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1

echo [OK] Permissions configurées

REM Créer un script de démarrage rapide
echo Création du script de démarrage...
echo @echo off > "%XIBO_PATH%\start-xibo.bat"
echo echo Démarrage de Xibo... >> "%XIBO_PATH%\start-xibo.bat"
echo start http://localhost/xibo/web/test.html >> "%XIBO_PATH%\start-xibo.bat"
echo start http://localhost/xibo/web/index.php >> "%XIBO_PATH%\start-xibo.bat"

echo [OK] Script de démarrage créé

echo.
echo ========================================
echo INSTALLATION TERMINÉE !
echo ========================================
echo.
echo Tests à effectuer:
echo 1. http://localhost/xibo/web/test.html
echo 2. http://localhost/xibo/web/index.php
echo 3. http://localhost/xibo/web/install/
echo.
echo Script de démarrage: %XIBO_PATH%\start-xibo.bat
echo.
echo Si ça ne fonctionne toujours pas, nous essaierons une autre version de Xibo.
echo.

REM Ouvrir automatiquement le navigateur
start http://localhost/xibo/web/test.html

pause
