# Configuration XAMPP pour Xibo Digital Signage

## 1. Configuration PHP (php.ini)
Fichier: C:\xampp\php\php.ini

### Extensions requises (décommenter si nécessaire):
extension=gd
extension=pdo_mysql
extension=zip
extension=curl
extension=soap
extension=mbstring
extension=fileinfo
extension=dom
extension=xml
extension=simplexml
extension=json
extension=ctype
extension=iconv

### Paramètres de performance:
memory_limit = 256M
upload_max_filesize = 128M
post_max_size = 128M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

### Paramètres de session:
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0

### Paramètres de timezone:
date.timezone = Europe/Paris

## 2. Configuration Apache

### Activer mod_rewrite
Dans httpd.conf, décommenter:
LoadModule rewrite_module modules/mod_rewrite.so

### Virtual Host (httpd-vhosts.conf)
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/xibo/web"
    ServerName xibo.local
    ServerAlias www.xibo.local
    
    <Directory "C:/xampp/htdocs/xibo/web">
        AllowOverride All
        Options Indexes FollowSymLinks MultiViews
        Require all granted
        DirectoryIndex index.php
    </Directory>
    
    # Logs pour debug
    ErrorLog "logs/xibo_error.log"
    CustomLog "logs/xibo_access.log" combined
</VirtualHost>

### Fichier .htaccess (déjà fourni par Xibo)
Le fichier web/.htaccess contient les règles de réécriture nécessaires.

## 3. Configuration MySQL

### Paramètres recommandés (my.ini):
[mysqld]
innodb_buffer_pool_size = 256M
max_allowed_packet = 64M
innodb_file_per_table = 1
sql_mode = ""

### Création de la base de données:
```sql
CREATE DATABASE xibo_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xibo_user'@'localhost' IDENTIFIED BY 'VotreMotDePasse123!';
GRANT ALL PRIVILEGES ON xibo_cms.* TO 'xibo_user'@'localhost';
FLUSH PRIVILEGES;
```

## 4. Structure des dossiers

### Arborescence recommandée:
C:\xampp\htdocs\xibo\
├── web\                    (DocumentRoot - accessible web)
│   ├── index.php
│   ├── .htaccess
│   └── ...
├── lib\                    (Bibliothèques PHP)
├── cache\                  (Cache temporaire)
├── library\                (Stockage des médias)
│   ├── certs\             (Certificats SSL)
│   └── ...
├── bin\                    (Scripts de maintenance)
│   └── xtr.php
└── vendor\                 (Dépendances Composer)

### Permissions Windows:
- Dossier xibo: Contrôle total pour IUSR et IIS_IUSRS
- Dossier library: Lecture/Écriture pour Apache
- Dossier cache: Lecture/Écriture pour Apache

## 5. Fichier hosts Windows

Ajouter dans C:\Windows\System32\drivers\etc\hosts:
```
127.0.0.1 xibo.local
127.0.0.1 www.xibo.local
```

## 6. Vérifications avant installation

### Checklist XAMPP:
□ Apache démarré et fonctionnel
□ MySQL démarré et accessible
□ PHP 8.1+ installé
□ Extensions PHP activées
□ mod_rewrite activé
□ Virtual Host configuré
□ Base de données créée
□ Permissions des dossiers correctes

### Test de configuration:
1. http://localhost - Page XAMPP
2. http://localhost/phpmyadmin - phpMyAdmin
3. http://xibo.local - Doit rediriger vers l'installateur Xibo

## 7. Paramètres de sécurité

### Recommandations:
- Changer le mot de passe MySQL root
- Utiliser un mot de passe fort pour xibo_user
- Désactiver les modules Apache non nécessaires
- Configurer un firewall local
- Sauvegarder régulièrement la base de données

### Fichier .htaccess sécurisé:
```apache
# Déjà inclus dans Xibo
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Sécurité supplémentaire
<Files "*.ini">
    Order allow,deny
    Deny from all
</Files>
```

## 8. Optimisations de performance

### PHP OPcache (php.ini):
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60

### MySQL optimisations:
innodb_buffer_pool_size = 50% de la RAM disponible
query_cache_size = 64M
query_cache_type = 1

## 9. Logs et monitoring

### Fichiers de logs importants:
- C:\xampp\apache\logs\error.log
- C:\xampp\mysql\data\mysql_error.log
- C:\xampp\htdocs\xibo\library\log\

### Surveillance:
- Espace disque du dossier library
- Performance de la base de données
- Logs d'erreurs Apache/PHP

## 10. Maintenance

### Tâches automatiques:
- Sauvegarde quotidienne de la base
- Nettoyage des logs anciens
- Vérification de l'espace disque
- Mise à jour de sécurité

### Script de sauvegarde:
```batch
@echo off
set BACKUP_DIR=C:\backups\xibo
set DATE=%date:~-4,4%%date:~-10,2%%date:~-7,2%

mysqldump -u xibo_user -p xibo_cms > %BACKUP_DIR%\xibo_cms_%DATE%.sql
xcopy "C:\xampp\htdocs\xibo\library" "%BACKUP_DIR%\library_%DATE%\" /E /I /Y
```
