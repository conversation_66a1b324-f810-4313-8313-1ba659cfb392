# Guide de test et validation de l'installation Xibo

## Tests de base - Vérification système

### 1. Test d'accès au CMS
```
✅ Accès principal
URL: http://xibo.local ou http://localhost/xibo
Résultat attendu: Page de connexion Xibo

✅ Connexion administrateur
Utilisateur: admin (ou celui créé)
Mot de passe: [votre mot de passe]
Résultat attendu: Tableau de bord Xibo
```

### 2. Test des permissions
```
✅ Test des dossiers
URL: http://localhost/xibo/test-permissions.php
Résultat attendu: Tous les dossiers marqués "OK"

✅ Upload de fichier
Menu: Library → Add Media
Action: Uploader une image (JPG/PNG)
Résultat attendu: Upload réussi, fichier visible
```

### 3. Test de la base de données
```
✅ Connexion MySQL
Outil: phpMyAdmin (http://localhost/phpmyadmin)
Base: xibo_cms
Résultat attendu: Tables Xibo présentes (50+ tables)

✅ Logs système
Menu: Administration → Log
Résultat attendu: Logs visibles, pas d'erreurs critiques
```

## Tests fonctionnels - Création de contenu

### 4. Créer votre premier Layout

#### Étape 1: Nouveau Layout
1. **Menu**: Layouts → Add Layout
2. **Nom**: "Test Layout"
3. **Description**: "Premier test d'affichage"
4. **Résolution**: 1920x1080 (Full HD)
5. **Cliquer**: Save

#### Étape 2: Ajouter du contenu
1. **Ouvrir**: Le layout créé
2. **Designer**: Glisser une région sur le canvas
3. **Ajouter**: Widget Text
4. **Contenu**: "Bienvenue sur Xibo!"
5. **Sauvegarder**: Le widget et le layout

#### Étape 3: Publier le layout
1. **Menu**: Layouts
2. **Actions**: Publish (sur votre layout)
3. **Confirmer**: La publication

### 5. Créer un Display virtuel

#### Configuration du Display
1. **Menu**: Displays → Add Display
2. **Nom**: "Test Display"
3. **Type**: Android/Windows (selon votre choix)
4. **Sauvegarder**: Le display

#### Programmer le contenu
1. **Menu**: Schedule → Add Event
2. **Display**: Sélectionner "Test Display"
3. **Layout**: Sélectionner "Test Layout"
4. **Horaire**: Maintenant → Dans 1 heure
5. **Sauvegarder**: L'événement

### 6. Test du Player (Simulation)

#### Player Web (pour test)
1. **Menu**: Displays
2. **Actions**: Screen Shot (sur Test Display)
3. **Résultat**: Aperçu du contenu programmé

#### Player Windows (si disponible)
1. **Télécharger**: Xibo Player Windows
2. **Installer**: Sur le même PC ou un autre
3. **Configurer**: Connexion au CMS
4. **Vérifier**: Affichage du contenu

## Tests avancés - Fonctionnalités

### 7. Test des médias

#### Images
```
✅ Upload JPG/PNG
Menu: Library → Add Media
Taille: < 10MB
Résultat: Miniature générée

✅ Utilisation dans layout
Action: Ajouter widget Image
Source: Fichier uploadé
Résultat: Image affichée correctement
```

#### Vidéos
```
✅ Upload MP4
Menu: Library → Add Media
Format: MP4, H.264
Résultat: Upload réussi

✅ Lecture dans layout
Widget: Video
Durée: Détectée automatiquement
Résultat: Vidéo jouable
```

### 8. Test des widgets

#### Widget Texte
- **Contenu**: Texte simple et formaté
- **Police**: Différentes tailles/couleurs
- **Animation**: Effets d'entrée/sortie

#### Widget Horloge
- **Format**: Date et heure
- **Style**: Personnalisation
- **Mise à jour**: Temps réel

#### Widget RSS
- **Source**: Flux RSS public
- **Affichage**: Défilement des actualités
- **Mise à jour**: Automatique

### 9. Test de planification

#### Événements ponctuels
```
✅ Événement immédiat
Début: Maintenant
Fin: Dans 10 minutes
Résultat: Contenu affiché immédiatement
```

#### Événements récurrents
```
✅ Planification quotidienne
Récurrence: Tous les jours
Heure: 09:00 - 17:00
Résultat: Répétition automatique
```

## Tests de performance

### 10. Test de charge

#### Upload multiple
- **Action**: Uploader 10 images simultanément
- **Taille**: 2-5MB chacune
- **Résultat**: Tous les uploads réussis

#### Layouts complexes
- **Création**: Layout avec 5+ régions
- **Contenu**: Mélange texte/image/vidéo
- **Performance**: Rendu fluide

### 11. Test de stabilité

#### Fonctionnement continu
```
✅ Test 24h
Action: Laisser le CMS fonctionner
Surveillance: Logs d'erreurs
Résultat: Aucune erreur critique
```

#### Redémarrage système
```
✅ Redémarrage PC
Action: Redémarrer le serveur
Vérification: Services auto-démarrés
Résultat: Xibo accessible après redémarrage
```

## Validation des tâches automatiques

### 12. Test XTR (Maintenance)

#### Vérification manuelle
```bash
# Exécuter XTR manuellement
cd C:\xampp\htdocs\xibo
C:\xampp\php\php.exe bin\xtr.php

# Résultat attendu: Exécution sans erreur
```

#### Tâche planifiée
```
✅ Planificateur Windows
Tâche: "Xibo Maintenance (XTR)"
Statut: "Prêt" après exécution
Historique: Exécutions réussies
```

### 13. Test des logs

#### Logs système
```
✅ Fichier: library/log/cms.log
Contenu: Activités récentes
Erreurs: Aucune erreur critique

✅ Fichier: library/log/xtr.log
Contenu: Exécutions XTR
Fréquence: Toutes les minutes
```

## Tests de sécurité

### 14. Accès sécurisé

#### Protection des dossiers
```
✅ Test: http://localhost/xibo/library/
Résultat: Accès interdit (403)

✅ Test: http://localhost/xibo/cache/
Résultat: Accès interdit (403)
```

#### Authentification
```
✅ Déconnexion forcée
Action: Fermer le navigateur
Reconnexion: Demande d'authentification
Résultat: Session sécurisée
```

## Checklist finale de validation

### ✅ Installation complète
- [ ] CMS accessible et fonctionnel
- [ ] Base de données opérationnelle
- [ ] Permissions correctement configurées
- [ ] Tâches automatiques actives

### ✅ Fonctionnalités de base
- [ ] Création de layouts réussie
- [ ] Upload de médias fonctionnel
- [ ] Planification opérationnelle
- [ ] Affichage de contenu correct

### ✅ Performance et stabilité
- [ ] Temps de réponse acceptables (< 3s)
- [ ] Aucune erreur critique dans les logs
- [ ] Redémarrage système sans problème
- [ ] Maintenance automatique active

### ✅ Sécurité
- [ ] Accès protégé aux dossiers sensibles
- [ ] Authentification obligatoire
- [ ] Mots de passe sécurisés
- [ ] Logs de sécurité actifs

## Prochaines étapes après validation

### 1. Configuration avancée
- Paramètres régionaux (timezone, langue)
- Configuration email (SMTP)
- Personnalisation de l'interface
- Intégrations tierces

### 2. Déploiement des players
- Installation Xibo Player sur devices
- Configuration réseau
- Tests d'affichage réel
- Monitoring des players

### 3. Création de contenu
- Templates de layouts
- Bibliothèque de médias
- Plannings d'affichage
- Contenu dynamique (RSS, API)

### 4. Maintenance et monitoring
- Sauvegardes automatiques
- Surveillance des performances
- Mises à jour de sécurité
- Formation des utilisateurs

---

**Félicitations !** Si tous les tests sont validés, votre installation Xibo est prête pour la production. Vous disposez maintenant d'un système d'affichage dynamique professionnel et fonctionnel.
